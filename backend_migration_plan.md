# POS App Backend Migration Plan

## 🎯 Target Architecture

### Backend Stack (Recommended)
- **Runtime**: Node.js 18+ with TypeScript
- **Framework**: Express.js with He<PERSON><PERSON>, CORS
- **Database**: PostgreSQL 15+ with connection pooling
- **Cache**: Redis for sessions and caching
- **Auth**: JWT with refresh tokens
- **File Storage**: MinIO (S3-compatible)
- **Real-time**: Socket.io for live updates
- **API Documentation**: Swagger/OpenAPI

### Database Schema Design

```sql
-- Users table (replaces Firebase Auth + Firestore users)
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL CHECK (role IN ('admin', 'employee')),
    outlet_id UUID REFERENCES outlets(id),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Outlets table
CREATE TABLE outlets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    address TEXT,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    phone VARCHAR(50),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Products table
CREATE TABLE products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    price DECIMAL(10, 2) NOT NULL,
    stock_quantity INTEGER DEFAULT 0,
    category VARCHAR(100),
    image_url VARCHAR(500),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Sales table (replaces Firestore sales)
CREATE TABLE sales (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    outlet_id UUID NOT NULL REFERENCES outlets(id),
    employee_id UUID NOT NULL REFERENCES users(id),
    subtotal DECIMAL(10, 2) NOT NULL,
    tax DECIMAL(10, 2) NOT NULL,
    discount DECIMAL(10, 2) DEFAULT 0,
    grand_total DECIMAL(10, 2) NOT NULL,
    payment_method VARCHAR(50) NOT NULL,
    sale_date TIMESTAMP NOT NULL,
    is_synced BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Sale items table
CREATE TABLE sale_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    sale_id UUID NOT NULL REFERENCES sales(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id),
    product_name VARCHAR(255) NOT NULL,
    price DECIMAL(10, 2) NOT NULL,
    quantity INTEGER NOT NULL,
    total DECIMAL(10, 2) NOT NULL
);

-- Attendance table
CREATE TABLE attendances (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    employee_id UUID NOT NULL REFERENCES users(id),
    outlet_id UUID NOT NULL REFERENCES outlets(id),
    check_in_time TIMESTAMP NOT NULL,
    check_out_time TIMESTAMP,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    is_synced BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 🔄 Migration Steps

### Step 1: Backend Setup
1. **Initialize Node.js project**
   ```bash
   mkdir pos-backend
   cd pos-backend
   npm init -y
   npm install express typescript @types/node
   npm install jsonwebtoken bcryptjs pg redis
   npm install socket.io multer helmet cors
   npm install -D nodemon ts-node @types/express
   ```

2. **Setup TypeScript configuration**
3. **Configure PostgreSQL connection**
4. **Implement JWT authentication**
5. **Create API endpoints**

### Step 2: Flutter App Changes
1. **Remove Firebase dependencies**
2. **Create HTTP client service**
3. **Update repository implementations**
4. **Implement JWT token management**
5. **Add WebSocket support**

### Step 3: Data Migration
1. **Export data from Firestore**
2. **Transform data format**
3. **Import to PostgreSQL**
4. **Verify data integrity**

### Step 4: Testing & Deployment
1. **Unit tests for backend APIs**
2. **Integration tests**
3. **Performance testing**
4. **Deploy to production**

## 📱 Flutter App Changes Required

### Dependencies to Remove
```yaml
# Remove from pubspec.yaml
firebase_core: ^3.13.0
firebase_auth: ^5.5.3
cloud_firestore: ^5.6.7
firebase_storage: ^12.4.5
```

### Dependencies to Add
```yaml
# Add to pubspec.yaml
http: ^1.1.0
dio: ^5.3.2  # Better HTTP client
socket_io_client: ^2.0.3
jwt_decoder: ^2.0.1
shared_preferences: ^2.2.2  # Token storage
```

### New Services Needed
1. **HTTP Client Service**
2. **Authentication Service** (JWT-based)
3. **WebSocket Service**
4. **Token Management Service**
5. **File Upload Service**

## 🔐 Security Considerations

### Authentication Flow
1. **Login**: Email/password → JWT access token + refresh token
2. **Token Storage**: Secure storage in device
3. **Auto-refresh**: Background token renewal
4. **Logout**: Token invalidation

### API Security
1. **Rate limiting**
2. **Input validation**
3. **SQL injection prevention**
4. **CORS configuration**
5. **Helmet.js security headers**

## 📊 Performance Optimizations

### Database
1. **Indexing strategy**
2. **Connection pooling**
3. **Query optimization**
4. **Pagination**

### Caching
1. **Redis for session storage**
2. **API response caching**
3. **Database query caching**

### Real-time Features
1. **Socket.io for live updates**
2. **Room-based notifications**
3. **Efficient event handling**

## 🚀 Deployment Strategy

### Development Environment
- **Docker Compose** for local development
- **PostgreSQL + Redis containers**
- **Hot reload for development**

### Production Environment
- **VPS/Cloud server** (DigitalOcean, AWS, etc.)
- **Nginx reverse proxy**
- **PM2 process manager**
- **SSL certificates**
- **Database backups**

## 📈 Monitoring & Logging

### Logging
- **Winston** for structured logging
- **Request/response logging**
- **Error tracking**

### Monitoring
- **Health check endpoints**
- **Performance metrics**
- **Database monitoring**

## 💰 Cost Comparison

### Firebase (Current)
- **Firestore**: $0.18/100K reads, $0.18/100K writes
- **Auth**: Free up to 50K MAU
- **Storage**: $0.026/GB/month

### Self-hosted (Proposed)
- **VPS**: $20-50/month (4GB RAM, 2 CPU)
- **Database**: Included in VPS
- **Storage**: Included in VPS
- **Total**: ~$30-60/month (predictable cost)

## ⏱️ Timeline Estimate

- **Week 1-2**: Backend setup and core APIs
- **Week 3**: Authentication and user management
- **Week 4**: POS and sales APIs
- **Week 5**: Flutter app migration
- **Week 6**: Testing and deployment
- **Week 7**: Data migration and go-live

**Total**: ~7 weeks for complete migration
