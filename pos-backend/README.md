# POS Backend API

A robust Point of Sale (POS) backend API built with NestJS, TypeScript, PostgreSQL, and Redis.

## 🚀 Features

- **Authentication & Authorization** - JWT-based auth with role-based access control
- **User Management** - Admin and employee user management
- **Outlet Management** - Multi-outlet support with location tracking
- **Product Management** - Product catalog with inventory tracking
- **Sales Processing** - Complete sales transaction handling with offline sync
- **Attendance Tracking** - Employee attendance with location verification
- **Real-time Updates** - WebSocket support for live notifications
- **Comprehensive Reporting** - Sales, attendance, and analytics reports
- **File Upload** - Image upload for products and users
- **API Documentation** - Auto-generated Swagger documentation

## 🛠️ Tech Stack

- **Framework**: NestJS with TypeScript
- **Database**: PostgreSQL with TypeORM
- **Cache**: Redis
- **Authentication**: JWT with Passport
- **Real-time**: Socket.io
- **Documentation**: Swagger/OpenAPI
- **Testing**: Jest
- **Containerization**: Docker & Docker Compose

## 📋 Prerequisites

- Node.js 18+ 
- PostgreSQL 15+
- Redis 7+
- Docker & Docker Compose (optional)

## 🚀 Quick Start

### 1. Clone and Install Dependencies

```bash
cd pos-backend
npm install
```

### 2. Environment Setup

```bash
cp .env.example .env
# Edit .env with your configuration
```

### 3. Start with Docker (Recommended)

```bash
# Start PostgreSQL and Redis
docker-compose up -d postgres redis

# Start the application
npm run start:dev
```

### 4. Manual Setup

```bash
# Start PostgreSQL and Redis manually
# Then run the application
npm run start:dev
```

## 📚 API Documentation

Once the server is running, visit:
- **Swagger UI**: http://localhost:3000/docs
- **API Base URL**: http://localhost:3000/api/v1

## 🗄️ Database

### Migrations

```bash
# Generate migration
npm run migration:generate -- src/database/migrations/InitialMigration

# Run migrations
npm run migration:run

# Revert migration
npm run migration:revert
```

### Seeding

```bash
# Seed database with initial data
npm run seed
```

## 🧪 Testing

```bash
# Unit tests
npm run test

# E2E tests
npm run test:e2e

# Test coverage
npm run test:cov
```

## 📁 Project Structure

```
src/
├── auth/                 # Authentication module
├── users/                # User management
├── outlets/              # Outlet management
├── products/             # Product management
├── sales/                # Sales processing
├── attendance/           # Attendance tracking
├── reports/              # Reporting module
├── websocket/            # Real-time notifications
├── common/               # Shared utilities
│   ├── decorators/       # Custom decorators
│   ├── filters/          # Exception filters
│   ├── guards/           # Auth guards
│   ├── interceptors/     # Request/response interceptors
│   └── services/         # Shared services
├── config/               # Configuration files
└── database/             # Database migrations & seeds
```

## 🔐 Authentication

The API uses JWT tokens for authentication:

1. **Login**: POST `/api/v1/auth/login`
2. **Refresh**: POST `/api/v1/auth/refresh`
3. **Logout**: POST `/api/v1/auth/logout`

Include the JWT token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

## 👥 User Roles

- **Admin**: Full access to all endpoints
- **Employee**: Limited access based on outlet assignment

## 🌐 WebSocket Events

Connect to WebSocket for real-time updates:

```javascript
const socket = io('http://localhost:3000');

// Join outlet room
socket.emit('join-outlet', { outletId: 'outlet-uuid' });

// Listen for events
socket.on('new-sale', (data) => console.log('New sale:', data));
socket.on('attendance-update', (data) => console.log('Attendance update:', data));
socket.on('stock-update', (data) => console.log('Stock update:', data));
```

## 📊 API Endpoints

### Authentication
- `POST /auth/login` - User login
- `POST /auth/refresh` - Refresh token
- `POST /auth/logout` - User logout
- `GET /auth/profile` - Get user profile

### Users
- `GET /users` - Get all users (Admin only)
- `POST /users` - Create user (Admin only)
- `GET /users/:id` - Get user by ID
- `PATCH /users/:id` - Update user (Admin only)
- `DELETE /users/:id` - Delete user (Admin only)

### Outlets
- `GET /outlets` - Get all outlets
- `POST /outlets` - Create outlet (Admin only)
- `GET /outlets/:id` - Get outlet by ID
- `PATCH /outlets/:id` - Update outlet (Admin only)
- `DELETE /outlets/:id` - Delete outlet (Admin only)

### Products
- `GET /products` - Get all products
- `POST /products` - Create product (Admin only)
- `GET /products/:id` - Get product by ID
- `PATCH /products/:id` - Update product (Admin only)
- `DELETE /products/:id` - Delete product (Admin only)

### Sales
- `GET /sales` - Get sales with filters
- `POST /sales` - Create new sale
- `POST /sales/batch` - Create multiple sales (offline sync)
- `GET /sales/:id` - Get sale by ID
- `GET /sales/reports/daily` - Daily sales report
- `GET /sales/reports/summary` - Sales summary

### Attendance
- `GET /attendance` - Get attendance records
- `POST /attendance/check-in` - Employee check-in
- `POST /attendance/check-out` - Employee check-out
- `GET /attendance/:id` - Get attendance by ID

### Reports
- `GET /reports/sales/daily` - Daily sales report
- `GET /reports/sales/monthly` - Monthly sales report
- `GET /reports/attendance/daily` - Daily attendance report
- `GET /reports/products/top-selling` - Top selling products
- `GET /reports/dashboard/summary` - Dashboard summary

## 🔧 Configuration

Key environment variables:

```env
# Application
NODE_ENV=development
PORT=3000

# Database
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=password
DB_DATABASE=pos_db

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379

# JWT
JWT_SECRET=your-secret-key
JWT_REFRESH_SECRET=your-refresh-secret
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d
```

## 🚀 Deployment

### Production Build

```bash
npm run build
npm run start:prod
```

### Docker Production

```bash
docker build -t pos-backend .
docker run -p 3000:3000 pos-backend
```

## 📝 Development Notes

This is a starter template with placeholder implementations. To complete the backend:

1. **Implement Authentication Logic** - Complete JWT auth flow
2. **Add Business Logic** - Implement actual CRUD operations
3. **Add Validation** - Create DTOs with proper validation
4. **Add Tests** - Write comprehensive unit and integration tests
5. **Add Migrations** - Create database migration files
6. **Add Seeding** - Create seed data for development

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## 📄 License

This project is licensed under the MIT License.
