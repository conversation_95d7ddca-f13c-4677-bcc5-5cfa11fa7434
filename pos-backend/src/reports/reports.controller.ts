import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { Api<PERSON><PERSON><PERSON>, ApiBearerAuth } from '@nestjs/swagger';
import { ReportsService } from './reports.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../common/decorators/roles.decorator';
import { UserRole } from '../users/enums/user-role.enum';

@ApiTags('Reports')
@Controller('reports')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth('JWT-auth')
export class ReportsController {
  constructor(private readonly reportsService: ReportsService) {}

  @Get('sales/daily')
  @Roles(UserRole.ADMIN)
  getDailySalesReport(
    @Query('date') date: string,
    @Query('outletId') outletId?: string,
  ) {
    return { message: 'Daily sales report endpoint - TODO: Implement' };
  }

  @Get('sales/monthly')
  @Roles(UserRole.ADMIN)
  getMonthlySalesReport(
    @Query('month') month: string,
    @Query('year') year: string,
    @Query('outletId') outletId?: string,
  ) {
    return { message: 'Monthly sales report endpoint - TODO: Implement' };
  }

  @Get('attendance/daily')
  @Roles(UserRole.ADMIN)
  getDailyAttendanceReport(
    @Query('date') date: string,
    @Query('outletId') outletId?: string,
  ) {
    return { message: 'Daily attendance report endpoint - TODO: Implement' };
  }

  @Get('products/top-selling')
  @Roles(UserRole.ADMIN)
  getTopSellingProducts(
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
    @Query('limit') limit?: number,
  ) {
    return { message: 'Top selling products report endpoint - TODO: Implement' };
  }

  @Get('dashboard/summary')
  @Roles(UserRole.ADMIN)
  getDashboardSummary(@Query('outletId') outletId?: string) {
    return { message: 'Dashboard summary endpoint - TODO: Implement' };
  }
}
