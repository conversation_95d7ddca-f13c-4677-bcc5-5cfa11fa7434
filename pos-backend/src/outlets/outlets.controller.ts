import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards } from '@nestjs/common';
import { ApiTags, ApiBearerAuth } from '@nestjs/swagger';
import { OutletsService } from './outlets.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('Outlets')
@Controller('outlets')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class OutletsController {
  constructor(private readonly outletsService: OutletsService) {}

  @Post()
  create(@Body() createOutletDto: any) {
    return { message: 'Create outlet endpoint - TODO: Implement' };
  }

  @Get()
  findAll() {
    return { message: 'Get all outlets endpoint - TODO: Implement' };
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return { message: `Get outlet ${id} endpoint - TODO: Implement` };
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateOutletDto: any) {
    return { message: `Update outlet ${id} endpoint - TODO: Implement` };
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return { message: `Delete outlet ${id} endpoint - TODO: Implement` };
  }
}
