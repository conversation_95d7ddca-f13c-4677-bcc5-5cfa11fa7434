import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { User } from '../../users/entities/user.entity';
import { Sale } from '../../sales/entities/sale.entity';
import { Attendance } from '../../attendance/entities/attendance.entity';

@Entity('outlets')
export class Outlet {
  @ApiProperty({ description: 'Outlet unique identifier' })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({ description: 'Outlet name' })
  @Column()
  name: string;

  @ApiProperty({ description: 'Outlet address' })
  @Column({ type: 'text', nullable: true })
  address: string;

  @ApiProperty({ description: 'Outlet latitude coordinate' })
  @Column({ type: 'decimal', precision: 10, scale: 8, nullable: true })
  latitude: number;

  @ApiProperty({ description: 'Outlet longitude coordinate' })
  @Column({ type: 'decimal', precision: 11, scale: 8, nullable: true })
  longitude: number;

  @ApiProperty({ description: 'Outlet phone number' })
  @Column({ nullable: true })
  phone: string;

  @ApiProperty({ description: 'Outlet image URL' })
  @Column({ name: 'image_url', nullable: true })
  imageUrl: string;

  @ApiProperty({ description: 'Whether outlet is active' })
  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @ApiProperty({ description: 'Outlet creation date' })
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @ApiProperty({ description: 'Outlet last update date' })
  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relations
  @OneToMany(() => User, (user) => user.outlet)
  employees: User[];

  @OneToMany(() => Sale, (sale) => sale.outlet)
  sales: Sale[];

  @OneToMany(() => Attendance, (attendance) => attendance.outlet)
  attendances: Attendance[];
}
