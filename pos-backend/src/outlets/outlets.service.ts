import { Injectable } from '@nestjs/common';

@Injectable()
export class OutletsService {
  create(createOutletDto: any) {
    return 'This action adds a new outlet';
  }

  findAll() {
    return `This action returns all outlets`;
  }

  findOne(id: string) {
    return `This action returns a #${id} outlet`;
  }

  update(id: string, updateOutletDto: any) {
    return `This action updates a #${id} outlet`;
  }

  remove(id: string) {
    return `This action removes a #${id} outlet`;
  }
}
