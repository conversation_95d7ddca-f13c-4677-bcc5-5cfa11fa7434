import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { OutletsController } from './outlets.controller';
import { OutletsService } from './outlets.service';
import { Outlet } from './entities/outlet.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Outlet])],
  controllers: [OutletsController],
  providers: [OutletsService],
  exports: [OutletsService],
})
export class OutletsModule {}
