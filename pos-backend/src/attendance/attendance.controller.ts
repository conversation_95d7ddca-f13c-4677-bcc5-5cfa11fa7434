import { Controller, Get, Post, Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiT<PERSON><PERSON>, ApiBearerAuth } from '@nestjs/swagger';
import { AttendanceService } from './attendance.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { GetUser } from '../common/decorators/get-user.decorator';
import { User } from '../users/entities/user.entity';

@ApiTags('Attendance')
@Controller('attendance')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class AttendanceController {
  constructor(private readonly attendanceService: AttendanceService) {}

  @Post('check-in')
  checkIn(@Body() checkInDto: any, @GetUser() user: User) {
    return { message: 'Check-in endpoint - TODO: Implement' };
  }

  @Post('check-out')
  checkOut(@Body() checkOutDto: any, @GetUser() user: User) {
    return { message: 'Check-out endpoint - TODO: Implement' };
  }

  @Get()
  findAll(@Query() query: any, @GetUser() user: User) {
    return { message: 'Get all attendance endpoint - TODO: Implement' };
  }

  @Get(':id')
  findOne(@Param('id') id: string, @GetUser() user: User) {
    return { message: `Get attendance ${id} endpoint - TODO: Implement` };
  }
}
