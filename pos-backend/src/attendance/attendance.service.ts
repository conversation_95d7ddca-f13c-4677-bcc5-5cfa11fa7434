import { Injectable } from '@nestjs/common';

@Injectable()
export class AttendanceService {
  checkIn(checkInDto: any) {
    return 'This action checks in an employee';
  }

  checkOut(checkOutDto: any) {
    return 'This action checks out an employee';
  }

  findAll() {
    return `This action returns all attendance records`;
  }

  findOne(id: string) {
    return `This action returns a #${id} attendance record`;
  }
}
