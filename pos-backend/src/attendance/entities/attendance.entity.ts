import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { User } from '../../users/entities/user.entity';
import { Outlet } from '../../outlets/entities/outlet.entity';

@Entity('attendances')
export class Attendance {
  @ApiProperty({ description: 'Attendance unique identifier' })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({ description: 'Employee ID' })
  @Column({ name: 'employee_id' })
  employeeId: string;

  @ApiProperty({ description: 'Outlet ID' })
  @Column({ name: 'outlet_id' })
  outletId: string;

  @ApiProperty({ description: 'Check-in time' })
  @Column({ name: 'check_in_time', type: 'timestamp' })
  checkInTime: Date;

  @ApiProperty({ description: 'Check-out time' })
  @Column({ name: 'check_out_time', type: 'timestamp', nullable: true })
  checkOutTime: Date;

  @ApiProperty({ description: 'Check-in latitude' })
  @Column({ type: 'decimal', precision: 10, scale: 8, nullable: true })
  latitude: number;

  @ApiProperty({ description: 'Check-in longitude' })
  @Column({ type: 'decimal', precision: 11, scale: 8, nullable: true })
  longitude: number;

  @ApiProperty({ description: 'Whether attendance is synced to server' })
  @Column({ name: 'is_synced', default: true })
  isSynced: boolean;

  @ApiProperty({ description: 'Attendance creation date' })
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  // Relations
  @ManyToOne(() => User, (user) => user.attendances)
  @JoinColumn({ name: 'employee_id' })
  employee: User;

  @ManyToOne(() => Outlet, (outlet) => outlet.attendances)
  @JoinColumn({ name: 'outlet_id' })
  outlet: Outlet;
}
