import {
  WebSocketGateway,
  SubscribeMessage,
  MessageBody,
  OnGatewayInit,
  OnGatewayConnection,
  OnGatewayDisconnect,
  WebSocketServer,
} from '@nestjs/websockets';
import { Logger } from '@nestjs/common';
import { Server, Socket } from 'socket.io';

@WebSocketGateway({
  cors: {
    origin: '*',
  },
})
export class WebsocketGateway
  implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect
{
  @WebSocketServer() server: Server;
  private logger: Logger = new Logger('WebsocketGateway');

  afterInit(server: Server) {
    this.logger.log('WebSocket Gateway initialized');
  }

  handleConnection(client: Socket, ...args: any[]) {
    this.logger.log(`Client connected: ${client.id}`);
  }

  handleDisconnect(client: Socket) {
    this.logger.log(`Client disconnected: ${client.id}`);
  }

  @SubscribeMessage('join-outlet')
  handleJoinOutlet(@MessageBody() data: { outletId: string }, client: Socket) {
    client.join(`outlet-${data.outletId}`);
    this.logger.log(`Client ${client.id} joined outlet ${data.outletId}`);
    return { event: 'joined-outlet', data: { outletId: data.outletId } };
  }

  @SubscribeMessage('leave-outlet')
  handleLeaveOutlet(@MessageBody() data: { outletId: string }, client: Socket) {
    client.leave(`outlet-${data.outletId}`);
    this.logger.log(`Client ${client.id} left outlet ${data.outletId}`);
    return { event: 'left-outlet', data: { outletId: data.outletId } };
  }

  // Emit events to specific outlet
  emitToOutlet(outletId: string, event: string, data: any) {
    this.server.to(`outlet-${outletId}`).emit(event, data);
  }

  // Emit events to all clients
  emitToAll(event: string, data: any) {
    this.server.emit(event, data);
  }

  // Notify new sale
  notifyNewSale(outletId: string, saleData: any) {
    this.emitToOutlet(outletId, 'new-sale', saleData);
  }

  // Notify attendance update
  notifyAttendanceUpdate(outletId: string, attendanceData: any) {
    this.emitToOutlet(outletId, 'attendance-update', attendanceData);
  }

  // Notify product stock update
  notifyStockUpdate(productData: any) {
    this.emitToAll('stock-update', productData);
  }
}
