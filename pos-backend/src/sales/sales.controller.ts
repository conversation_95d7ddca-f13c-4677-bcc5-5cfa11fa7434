import { Controller, Get, Post, Body, Param, Query, UseGuards } from '@nestjs/common';
import { Api<PERSON><PERSON><PERSON>, ApiBearerAuth } from '@nestjs/swagger';
import { SalesService } from './sales.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { GetUser } from '../common/decorators/get-user.decorator';
import { User } from '../users/entities/user.entity';

@ApiTags('Sales')
@Controller('sales')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class SalesController {
  constructor(private readonly salesService: SalesService) {}

  @Post()
  create(@Body() createSaleDto: any, @GetUser() user: User) {
    return { message: 'Create sale endpoint - TODO: Implement' };
  }

  @Post('batch')
  createBatch(@Body() batchSalesDto: any, @GetUser() user: User) {
    return { message: 'Create batch sales endpoint - TODO: Implement' };
  }

  @Get()
  findAll(@Query() query: any, @GetUser() user: User) {
    return { message: 'Get all sales endpoint - TODO: Implement' };
  }

  @Get(':id')
  findOne(@Param('id') id: string, @GetUser() user: User) {
    return { message: `Get sale ${id} endpoint - TODO: Implement` };
  }

  @Get('reports/daily')
  getDailyReport(@Query('date') date: string, @Query('outletId') outletId?: string) {
    return { message: 'Daily sales report endpoint - TODO: Implement' };
  }

  @Get('reports/summary')
  getSummary(
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
    @Query('outletId') outletId?: string,
  ) {
    return { message: 'Sales summary endpoint - TODO: Implement' };
  }
}
