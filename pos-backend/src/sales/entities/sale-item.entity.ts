import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Sale } from './sale.entity';
import { Product } from '../../products/entities/product.entity';

@Entity('sale_items')
export class SaleItem {
  @ApiProperty({ description: 'Sale item unique identifier' })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({ description: 'Sale ID' })
  @Column({ name: 'sale_id' })
  saleId: string;

  @ApiProperty({ description: 'Product ID' })
  @Column({ name: 'product_id' })
  productId: string;

  @ApiProperty({ description: 'Product name at time of sale' })
  @Column({ name: 'product_name' })
  productName: string;

  @ApiProperty({ description: 'Product price at time of sale' })
  @Column({ type: 'decimal', precision: 10, scale: 2 })
  price: number;

  @ApiProperty({ description: 'Quantity sold' })
  @Column()
  quantity: number;

  @ApiProperty({ description: 'Total amount for this item' })
  @Column({ type: 'decimal', precision: 10, scale: 2 })
  total: number;

  // Relations
  @ManyToOne(() => Sale, (sale) => sale.items, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'sale_id' })
  sale: Sale;

  @ManyToOne(() => Product, (product) => product.saleItems)
  @JoinColumn({ name: 'product_id' })
  product: Product;
}
