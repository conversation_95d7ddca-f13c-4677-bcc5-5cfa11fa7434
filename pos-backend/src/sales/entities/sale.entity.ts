import {
  <PERSON>ti<PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  OneToMany,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { PaymentMethod } from '../enums/payment-method.enum';
import { User } from '../../users/entities/user.entity';
import { Outlet } from '../../outlets/entities/outlet.entity';
import { SaleItem } from './sale-item.entity';

@Entity('sales')
export class Sale {
  @ApiProperty({ description: 'Sale unique identifier' })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({ description: 'Outlet ID' })
  @Column({ name: 'outlet_id' })
  outletId: string;

  @ApiProperty({ description: 'Employee ID' })
  @Column({ name: 'employee_id' })
  employeeId: string;

  @ApiProperty({ description: 'Sale subtotal amount' })
  @Column({ type: 'decimal', precision: 10, scale: 2 })
  subtotal: number;

  @ApiProperty({ description: 'Tax amount' })
  @Column({ type: 'decimal', precision: 10, scale: 2 })
  tax: number;

  @ApiProperty({ description: 'Discount amount' })
  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  discount: number;

  @ApiProperty({ description: 'Grand total amount' })
  @Column({ name: 'grand_total', type: 'decimal', precision: 10, scale: 2 })
  grandTotal: number;

  @ApiProperty({ description: 'Payment method', enum: PaymentMethod })
  @Column({
    name: 'payment_method',
    type: 'enum',
    enum: PaymentMethod,
  })
  paymentMethod: PaymentMethod;

  @ApiProperty({ description: 'Sale date and time' })
  @Column({ name: 'sale_date', type: 'timestamp' })
  saleDate: Date;

  @ApiProperty({ description: 'Whether sale is synced to server' })
  @Column({ name: 'is_synced', default: true })
  isSynced: boolean;

  @ApiProperty({ description: 'Sale creation date' })
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  // Relations
  @ManyToOne(() => Outlet, (outlet) => outlet.sales)
  @JoinColumn({ name: 'outlet_id' })
  outlet: Outlet;

  @ManyToOne(() => User, (user) => user.sales)
  @JoinColumn({ name: 'employee_id' })
  employee: User;

  @OneToMany(() => SaleItem, (saleItem) => saleItem.sale, { cascade: true })
  items: SaleItem[];
}
