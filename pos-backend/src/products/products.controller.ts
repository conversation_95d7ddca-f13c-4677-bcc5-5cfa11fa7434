import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards } from '@nestjs/common';
import { ApiTags, ApiBearerAuth } from '@nestjs/swagger';
import { ProductsService } from './products.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('Products')
@Controller('products')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class ProductsController {
  constructor(private readonly productsService: ProductsService) {}

  @Post()
  create(@Body() createProductDto: any) {
    return { message: 'Create product endpoint - TODO: Implement' };
  }

  @Get()
  findAll() {
    return { message: 'Get all products endpoint - TODO: Implement' };
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return { message: `Get product ${id} endpoint - TODO: Implement` };
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateProductDto: any) {
    return { message: `Update product ${id} endpoint - TODO: Implement` };
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return { message: `Delete product ${id} endpoint - TODO: Implement` };
  }
}
