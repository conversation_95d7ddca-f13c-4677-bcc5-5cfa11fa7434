import {
  <PERSON>ti<PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { SaleItem } from '../../sales/entities/sale-item.entity';

@Entity('products')
export class Product {
  @ApiProperty({ description: 'Product unique identifier' })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({ description: 'Product name' })
  @Column()
  name: string;

  @ApiProperty({ description: 'Product description' })
  @Column({ type: 'text', nullable: true })
  description: string;

  @ApiProperty({ description: 'Product price' })
  @Column({ type: 'decimal', precision: 10, scale: 2 })
  price: number;

  @ApiProperty({ description: 'Product stock quantity' })
  @Column({ name: 'stock_quantity', default: 0 })
  stockQuantity: number;

  @ApiProperty({ description: 'Product category' })
  @Column({ nullable: true })
  category: string;

  @ApiProperty({ description: 'Product image URL' })
  @Column({ name: 'image_url', nullable: true })
  imageUrl: string;

  @ApiProperty({ description: 'Product barcode' })
  @Column({ nullable: true })
  barcode: string;

  @ApiProperty({ description: 'Product SKU' })
  @Column({ nullable: true })
  sku: string;

  @ApiProperty({ description: 'Whether product is active' })
  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @ApiProperty({ description: 'Product creation date' })
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @ApiProperty({ description: 'Product last update date' })
  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relations
  @OneToMany(() => SaleItem, (saleItem) => saleItem.product)
  saleItems: SaleItem[];
}
