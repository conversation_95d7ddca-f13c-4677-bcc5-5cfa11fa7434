import { Injectable, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { extname } from 'path';
import { v4 as uuidv4 } from 'uuid';
import * as fs from 'fs';
import * as path from 'path';

@Injectable()
export class FileUploadService {
  private readonly uploadPath: string;
  private readonly maxFileSize: number;
  private readonly allowedMimeTypes: string[];

  constructor(private configService: ConfigService) {
    this.uploadPath = this.configService.get('UPLOAD_DEST', './uploads');
    this.maxFileSize = this.configService.get('MAX_FILE_SIZE', 5242880); // 5MB
    this.allowedMimeTypes = [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'application/pdf',
    ];

    // Ensure upload directory exists
    this.ensureUploadDir();
  }

  private ensureUploadDir(): void {
    if (!fs.existsSync(this.uploadPath)) {
      fs.mkdirSync(this.uploadPath, { recursive: true });
    }

    // Create subdirectories
    const subdirs = ['products', 'users', 'outlets', 'temp'];
    subdirs.forEach(subdir => {
      const subdirPath = path.join(this.uploadPath, subdir);
      if (!fs.existsSync(subdirPath)) {
        fs.mkdirSync(subdirPath, { recursive: true });
      }
    });
  }

  generateFileName(originalName: string): string {
    const ext = extname(originalName);
    const name = uuidv4();
    return `${name}${ext}`;
  }

  validateFile(file: Express.Multer.File): void {
    // Check file size
    if (file.size > this.maxFileSize) {
      throw new BadRequestException(
        `File size too large. Maximum size is ${this.maxFileSize / 1024 / 1024}MB`,
      );
    }

    // Check mime type
    if (!this.allowedMimeTypes.includes(file.mimetype)) {
      throw new BadRequestException(
        `File type not allowed. Allowed types: ${this.allowedMimeTypes.join(', ')}`,
      );
    }
  }

  getFileUrl(filename: string, category: string = ''): string {
    const baseUrl = this.configService.get('BASE_URL', 'http://localhost:3000');
    const filePath = category ? `${category}/${filename}` : filename;
    return `${baseUrl}/uploads/${filePath}`;
  }

  async deleteFile(filename: string, category: string = ''): Promise<void> {
    try {
      const filePath = category 
        ? path.join(this.uploadPath, category, filename)
        : path.join(this.uploadPath, filename);

      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    } catch (error) {
      // Log error but don't throw - file deletion is not critical
      console.error('Error deleting file:', error);
    }
  }

  getUploadPath(category: string = ''): string {
    return category 
      ? path.join(this.uploadPath, category)
      : this.uploadPath;
  }
}
