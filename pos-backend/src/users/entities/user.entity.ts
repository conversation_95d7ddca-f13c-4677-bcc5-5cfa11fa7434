import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  OneToMany,
} from 'typeorm';
import { Exclude } from 'class-transformer';
import { ApiProperty, ApiHideProperty } from '@nestjs/swagger';
import { UserRole } from '../enums/user-role.enum';
import { Outlet } from '../../outlets/entities/outlet.entity';
import { Sale } from '../../sales/entities/sale.entity';
import { Attendance } from '../../attendance/entities/attendance.entity';

@Entity('users')
export class User {
  @ApiProperty({ description: 'User unique identifier' })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({ description: 'User email address' })
  @Column({ unique: true })
  email: string;

  @ApiHideProperty()
  @Column({ name: 'password_hash' })
  @Exclude()
  passwordHash: string;

  @ApiProperty({ description: 'User full name' })
  @Column()
  name: string;

  @ApiProperty({ description: 'User role', enum: UserRole })
  @Column({
    type: 'enum',
    enum: UserRole,
    default: UserRole.EMPLOYEE,
  })
  role: UserRole;

  @ApiProperty({ description: 'Outlet ID (for employees)' })
  @Column({ name: 'outlet_id', nullable: true })
  outletId: string;

  @ApiProperty({ description: 'User profile image URL' })
  @Column({ name: 'profile_image', nullable: true })
  profileImage: string;

  @ApiProperty({ description: 'User phone number' })
  @Column({ nullable: true })
  phone: string;

  @ApiProperty({ description: 'Whether user is active' })
  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @ApiProperty({ description: 'User creation date' })
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @ApiProperty({ description: 'User last update date' })
  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relations
  @ManyToOne(() => Outlet, (outlet) => outlet.employees, { nullable: true })
  @JoinColumn({ name: 'outlet_id' })
  outlet: Outlet;

  @OneToMany(() => Sale, (sale) => sale.employee)
  sales: Sale[];

  @OneToMany(() => Attendance, (attendance) => attendance.employee)
  attendances: Attendance[];
}
