{"version": 3, "sources": ["../browser/src/schema-builder/options/TableColumnOptions.ts"], "names": [], "mappings": "", "file": "TableColumnOptions.js", "sourcesContent": ["/**\n * Table's column options.\n */\nexport interface TableColumnOptions {\n    // -------------------------------------------------------------------------\n    // Public Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Column name.\n     */\n    name: string\n\n    /**\n     * Column type.\n     */\n    type: string\n\n    /**\n     * Column's default value.\n     */\n    default?: any\n\n    /**\n     * ON UPDATE trigger. Works only for MySQL.\n     */\n    onUpdate?: string\n\n    /**\n     * Indicates if column is NULL, or is NOT NULL in the database.\n     */\n    isNullable?: boolean\n\n    /**\n     * Indicates if column is auto-generated sequence.\n     */\n    isGenerated?: boolean\n\n    /**\n     * Specifies generation strategy if this column will use auto increment.\n     */\n    generationStrategy?: \"uuid\" | \"increment\" | \"rowid\" | \"identity\"\n\n    /**\n     * Indicates if column is a primary key.\n     */\n    isPrimary?: boolean\n\n    /**\n     * Indicates if column has unique value.\n     */\n    isUnique?: boolean\n\n    /**\n     * Indicates if column stores array.\n     */\n    isArray?: boolean\n\n    /**\n     * Column's comment.\n     */\n    comment?: string\n\n    /**\n     * Column type's length. Used only on some column types.\n     * For example type = \"string\" and length = \"100\" means that ORM will create a column with type varchar(100).\n     */\n    length?: string\n\n    /**\n     * Column type's display width. Used only on some column types in MySQL.\n     * For example, INT(4) specifies an INT with a display width of four digits.\n     */\n    width?: number\n\n    /**\n     * Defines column character set.\n     */\n    charset?: string\n\n    /**\n     * Defines column collation.\n     */\n    collation?: string\n\n    /**\n     * The precision for a decimal (exact numeric) column (applies only for decimal column), which is the maximum\n     * number of digits that are stored for the values.\n     */\n    precision?: number | null\n\n    /**\n     * The scale for a decimal (exact numeric) column (applies only for decimal column), which represents the number\n     * of digits to the right of the decimal point and must not be greater than precision.\n     */\n    scale?: number\n\n    /**\n     * Puts ZEROFILL attribute on to numeric column. Works only for MySQL.\n     * If you specify ZEROFILL for a numeric column, MySQL automatically adds the UNSIGNED attribute to the column\n     */\n    zerofill?: boolean\n\n    /**\n     * Puts UNSIGNED attribute on to numeric column. Works only for MySQL.\n     */\n    unsigned?: boolean\n\n    /**\n     * Array of possible enumerated values.\n     */\n    enum?: string[]\n\n    /**\n     * Exact name of enum\n     */\n    enumName?: string\n\n    /**\n     * If this column is primary key then this specifies the name for it.\n     */\n    primaryKeyConstraintName?: string\n\n    /**\n     * If this column is foreign key then this specifies the name for it.\n     */\n    foreignKeyConstraintName?: string\n\n    /**\n     * Generated column expression.\n     */\n    asExpression?: string\n\n    /**\n     * Generated column type.\n     */\n    generatedType?: \"VIRTUAL\" | \"STORED\"\n\n    /**\n     * Identity column type. Supports only in Postgres 10+.\n     */\n    generatedIdentity?: \"ALWAYS\" | \"BY DEFAULT\"\n\n    /**\n     * Spatial Feature Type (Geometry, Point, Polygon, etc.)\n     */\n    spatialFeatureType?: string\n\n    /**\n     * SRID (Spatial Reference ID (EPSG code))\n     */\n    srid?: number\n}\n"], "sourceRoot": "../.."}