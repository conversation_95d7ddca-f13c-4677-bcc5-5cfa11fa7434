{"version": 3, "sources": ["../../src/query-builder/WhereExpressionBuilder.ts"], "names": [], "mappings": "", "file": "WhereExpressionBuilder.js", "sourcesContent": ["import { ObjectLiteral } from \"../common/ObjectLiteral\"\nimport { Brackets } from \"./Brackets\"\n\n/**\n * Query Builders can implement this interface to support where expression\n */\nexport interface WhereExpressionBuilder {\n    /**\n     * Sets WHERE condition in the query builder.\n     * If you had previously WHERE expression defined,\n     * calling this function will override previously set WHERE conditions.\n     * Additionally you can add parameters used in where expression.\n     */\n    where(where: string, parameters?: ObjectLiteral): this\n\n    /**\n     * Sets WHERE condition in the query builder.\n     * If you had previously WHERE expression defined,\n     * calling this function will override previously set WHERE conditions.\n     * Additionally you can add parameters used in where expression.\n     */\n    where(where: Brackets, parameters?: ObjectLiteral): this\n\n    /**\n     * Sets WHERE condition in the query builder.\n     * If you had previously WHERE expression defined,\n     * calling this function will override previously set WHERE conditions.\n     * Additionally you can add parameters used in where expression.\n     */\n    where(where: ObjectLiteral, parameters?: ObjectLiteral): this\n\n    /**\n     * Sets WHERE condition in the query builder.\n     * If you had previously WHERE expression defined,\n     * calling this function will override previously set WHERE conditions.\n     * Additionally you can add parameters used in where expression.\n     */\n    where(where: ObjectLiteral[], parameters?: ObjectLiteral): this\n\n    /**\n     * Sets WHERE condition in the query builder.\n     * If you had previously WHERE expression defined,\n     * calling this function will override previously set WHERE conditions.\n     * Additionally you can add parameters used in where expression.\n     */\n    where(subQuery: (qb: this) => string, parameters?: ObjectLiteral): this\n\n    /**\n     * Adds new AND WHERE condition in the query builder.\n     * Additionally you can add parameters used in where expression.\n     */\n    andWhere(where: string, parameters?: ObjectLiteral): this\n\n    /**\n     * Adds new AND WHERE condition in the query builder.\n     * Additionally you can add parameters used in where expression.\n     */\n    andWhere(where: Brackets, parameters?: ObjectLiteral): this\n\n    /**\n     * Adds new AND WHERE condition in the query builder.\n     * Additionally you can add parameters used in where expression.\n     */\n    andWhere(where: ObjectLiteral, parameters?: ObjectLiteral): this\n\n    /**\n     * Adds new AND WHERE condition in the query builder.\n     * Additionally you can add parameters used in where expression.\n     */\n    andWhere(where: ObjectLiteral[], parameters?: ObjectLiteral): this\n\n    /**\n     * Adds new AND WHERE condition in the query builder.\n     * Additionally you can add parameters used in where expression.\n     */\n    andWhere(subQuery: (qb: this) => string, parameters?: ObjectLiteral): this\n\n    /**\n     * Adds new OR WHERE condition in the query builder.\n     * Additionally you can add parameters used in where expression.\n     */\n    orWhere(where: string, parameters?: ObjectLiteral): this\n\n    /**\n     * Adds new OR WHERE condition in the query builder.\n     * Additionally you can add parameters used in where expression.\n     */\n    orWhere(where: Brackets, parameters?: ObjectLiteral): this\n\n    /**\n     * Adds new OR WHERE condition in the query builder.\n     * Additionally you can add parameters used in where expression.\n     */\n    orWhere(where: ObjectLiteral, parameters?: ObjectLiteral): this\n\n    /**\n     * Adds new OR WHERE condition in the query builder.\n     * Additionally you can add parameters used in where expression.\n     */\n    orWhere(where: ObjectLiteral[], parameters?: ObjectLiteral): this\n\n    /**\n     * Adds new OR WHERE condition in the query builder.\n     * Additionally you can add parameters used in where expression.\n     */\n    orWhere(subQuery: (qb: this) => string, parameters?: ObjectLiteral): this\n\n    /**\n     * Sets WHERE condition in the query builder with a condition for the given ids.\n     * If you had previously WHERE expression defined,\n     * calling this function will override previously set WHERE conditions.\n     *\n     * Ids are mixed.\n     * It means if you have single primary key you can pass a simple id values, for example [1, 2, 3].\n     * If you have multiple primary keys you need to pass object with property names and values specified,\n     * for example [{ firstId: 1, secondId: 2 }, { firstId: 2, secondId: 3 }, ...]\n     */\n    whereInIds(ids: any | any[]): this\n\n    /**\n     * Adds new AND WHERE with conditions for the given ids.\n     *\n     * Ids are mixed.\n     * It means if you have single primary key you can pass a simple id values, for example [1, 2, 3].\n     * If you have multiple primary keys you need to pass object with property names and values specified,\n     * for example [{ firstId: 1, secondId: 2 }, { firstId: 2, secondId: 3 }, ...]\n     */\n    andWhereInIds(ids: any | any[]): this\n\n    /**\n     * Adds new OR WHERE with conditions for the given ids.\n     *\n     * Ids are mixed.\n     * It means if you have single primary key you can pass a simple id values, for example [1, 2, 3].\n     * If you have multiple primary keys you need to pass object with property names and values specified,\n     * for example [{ firstId: 1, secondId: 2 }, { firstId: 2, secondId: 3 }, ...]\n     */\n    orWhereInIds(ids: any | any[]): this\n}\n\n/**\n * @deprecated Use `WhereExpressionBuilder` instead\n */\nexport interface WhereExpression extends WhereExpressionBuilder {}\n"], "sourceRoot": ".."}