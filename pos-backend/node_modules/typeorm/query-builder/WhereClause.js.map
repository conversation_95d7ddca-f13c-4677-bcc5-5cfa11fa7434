{"version": 3, "sources": ["../../src/query-builder/WhereClause.ts"], "names": [], "mappings": "", "file": "WhereClause.js", "sourcesContent": ["type WrappingOperator = \"not\" | \"brackets\"\n\ntype PredicateOperator =\n    | \"lessThan\"\n    | \"lessThanOrEqual\"\n    | \"moreThan\"\n    | \"moreThanOrEqual\"\n    | \"equal\"\n    | \"notEqual\"\n    | \"ilike\"\n    | \"like\"\n    | \"between\"\n    | \"in\"\n    | \"any\"\n    | \"isNull\"\n    | \"arrayContains\"\n    | \"arrayContainedBy\"\n    | \"arrayOverlap\"\n    | \"and\"\n    | \"jsonContains\"\n    | \"or\"\n\nexport interface WherePredicateOperator {\n    operator: PredicateOperator\n\n    parameters: string[]\n}\n\nexport interface WhereWrappingOperator {\n    operator: WrappingOperator\n\n    condition: WhereClauseCondition\n}\n\nexport interface WhereClause {\n    type: \"simple\" | \"and\" | \"or\"\n\n    condition: WhereClauseCondition\n}\n\nexport type WhereClauseCondition =\n    | string\n    | WherePredicateOperator\n    | WhereWrappingOperator\n    | WhereClause[]\n"], "sourceRoot": ".."}