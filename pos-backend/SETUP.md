# POS Backend Setup Guide

## 🚀 Quick Start Guide

### 1. Prerequisites Check

Make sure you have the following installed:
- **Node.js 18+** - [Download here](https://nodejs.org/)
- **PostgreSQL 15+** - [Download here](https://www.postgresql.org/download/)
- **Redis 7+** - [Download here](https://redis.io/download)
- **Docker & Docker Compose** (optional but recommended)

### 2. Installation Steps

#### Option A: Using Docker (Recommended)

```bash
# 1. Navigate to backend directory
cd pos-backend

# 2. Copy environment file
cp .env.example .env

# 3. Start services with Docker
docker-compose up -d

# 4. Install dependencies
npm install

# 5. Run database migrations (if any)
npm run migration:run

# 6. Seed the database
npm run seed

# 7. Start development server
npm run start:dev
```

#### Option B: Manual Setup

```bash
# 1. Install dependencies
cd pos-backend
npm install

# 2. Setup PostgreSQL database
createdb pos_db

# 3. Setup Redis (start Redis server)
redis-server

# 4. Copy and configure environment
cp .env.example .env
# Edit .env with your database credentials

# 5. Run database setup
npm run migration:run
npm run seed

# 6. Start development server
npm run start:dev
```

### 3. Verify Installation

Once the server is running, you should see:

```
🚀 Application is running on: http://localhost:3000/api/v1
📚 Swagger docs: http://localhost:3000/docs
🌍 Environment: development
```

Visit these URLs to verify:
- **API Health**: http://localhost:3000/api/v1/health
- **Swagger Documentation**: http://localhost:3000/docs

### 4. Test the API

#### Login as Admin
```bash
curl -X POST http://localhost:3000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "admin123"
  }'
```

#### Login as Employee
```bash
curl -X POST http://localhost:3000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "employee123"
  }'
```

## 🔧 Configuration

### Environment Variables

Edit `.env` file with your configuration:

```env
# Application
NODE_ENV=development
PORT=3000
API_PREFIX=api/v1

# Database
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=your_password
DB_DATABASE=pos_db

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT
JWT_SECRET=your-super-secret-jwt-key-change-this
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-this
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# File Upload
MAX_FILE_SIZE=5242880
UPLOAD_DEST=./uploads

# CORS
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080
```

### Database Configuration

If using custom PostgreSQL settings:

```env
DB_HOST=your_db_host
DB_PORT=5432
DB_USERNAME=your_username
DB_PASSWORD=your_password
DB_DATABASE=pos_db
```

## 🗄️ Database Setup

### Manual Database Creation

```sql
-- Connect to PostgreSQL as superuser
psql -U postgres

-- Create database
CREATE DATABASE pos_db;

-- Create user (optional)
CREATE USER pos_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE pos_db TO pos_user;
```

### Running Migrations

```bash
# Generate new migration
npm run migration:generate -- src/database/migrations/YourMigrationName

# Run all pending migrations
npm run migration:run

# Revert last migration
npm run migration:revert
```

### Seeding Data

```bash
# Seed database with sample data
npm run seed
```

This will create:
- 2 outlets (Main Store, Branch Store)
- 1 admin user (<EMAIL> / admin123)
- 2 employee users (<EMAIL>, <EMAIL> / employee123)
- 5 sample products
- Sample sales and attendance data

## 🧪 Testing

### Run Tests

```bash
# Unit tests
npm run test

# Watch mode
npm run test:watch

# E2E tests
npm run test:e2e

# Test coverage
npm run test:cov
```

### Manual API Testing

Use the Swagger UI at http://localhost:3000/docs or tools like:
- **Postman**
- **Insomnia**
- **curl**
- **HTTPie**

## 🐳 Docker Setup

### Development with Docker

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f api

# Stop services
docker-compose down

# Rebuild and start
docker-compose up -d --build
```

### Production Docker Build

```bash
# Build production image
docker build -t pos-backend:latest .

# Run production container
docker run -p 3000:3000 \
  -e NODE_ENV=production \
  -e DB_HOST=your_db_host \
  -e REDIS_HOST=your_redis_host \
  pos-backend:latest
```

## 🔍 Troubleshooting

### Common Issues

#### 1. Database Connection Error
```
Error: connect ECONNREFUSED 127.0.0.1:5432
```
**Solution**: Make sure PostgreSQL is running and credentials are correct.

#### 2. Redis Connection Error
```
Error: connect ECONNREFUSED 127.0.0.1:6379
```
**Solution**: Make sure Redis server is running.

#### 3. Port Already in Use
```
Error: listen EADDRINUSE: address already in use :::3000
```
**Solution**: Change PORT in .env or kill the process using port 3000.

#### 4. JWT Secret Warning
```
Warning: JWT secret is not secure
```
**Solution**: Change JWT_SECRET and JWT_REFRESH_SECRET in .env to secure random strings.

### Debug Mode

Enable debug logging:

```env
LOG_LEVEL=debug
```

### Health Check

Check if all services are running:

```bash
# API health
curl http://localhost:3000/api/v1/health

# Database connection
npm run typeorm -- query "SELECT NOW()"

# Redis connection
redis-cli ping
```

## 📚 Next Steps

1. **Implement Business Logic**: Complete the TODO implementations in services
2. **Add Validation**: Create proper DTOs with validation decorators
3. **Write Tests**: Add comprehensive unit and integration tests
4. **Add Authentication**: Complete the JWT authentication flow
5. **Add File Upload**: Implement image upload for products and users
6. **Add Real-time Features**: Complete WebSocket implementation
7. **Add Monitoring**: Set up logging and monitoring tools

## 🤝 Development Workflow

1. **Create Feature Branch**: `git checkout -b feature/your-feature`
2. **Make Changes**: Implement your feature
3. **Run Tests**: `npm run test`
4. **Check Linting**: `npm run lint`
5. **Commit Changes**: `git commit -m "feat: your feature"`
6. **Push Branch**: `git push origin feature/your-feature`
7. **Create PR**: Submit pull request for review

## 📞 Support

If you encounter any issues:

1. Check this setup guide
2. Review the troubleshooting section
3. Check the logs: `docker-compose logs api`
4. Create an issue in the repository

Happy coding! 🚀
