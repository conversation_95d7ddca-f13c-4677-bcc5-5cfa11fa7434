import { DataSource } from 'typeorm';
import * as bcrypt from 'bcryptjs';
import { dataSourceOptions } from '../../src/config/database.config';

async function seed() {
  const dataSource = new DataSource(dataSourceOptions);
  await dataSource.initialize();

  console.log('🌱 Starting database seeding...');

  try {
    // Create outlets
    const outletQuery = `
      INSERT INTO outlets (id, name, address, latitude, longitude, phone) VALUES
      ('550e8400-e29b-41d4-a716-446655440001', 'Main Store', 'Jl. Sudirman No. 123, Jakarta', -6.2088, 106.8456, '+62-21-12345678'),
      ('550e8400-e29b-41d4-a716-446655440002', 'Branch Store', 'Jl. Thamrin No. 456, Jakarta', -6.1944, 106.8229, '+62-21-87654321')
      ON CONFLICT (id) DO NOTHING;
    `;
    await dataSource.query(outletQuery);
    console.log('✅ Outlets seeded');

    // Create admin user
    const adminPassword = await bcrypt.hash('admin123', 10);
    const adminQuery = `
      INSERT INTO users (id, email, password_hash, name, role) VALUES
      ('550e8400-e29b-41d4-a716-446655440010', '<EMAIL>', $1, 'System Administrator', 'admin')
      ON CONFLICT (email) DO NOTHING;
    `;
    await dataSource.query(adminQuery, [adminPassword]);
    console.log('✅ Admin user seeded (email: <EMAIL>, password: admin123)');

    // Create employee users
    const employeePassword = await bcrypt.hash('employee123', 10);
    const employeeQuery = `
      INSERT INTO users (id, email, password_hash, name, role, outlet_id) VALUES
      ('550e8400-e29b-41d4-a716-446655440011', '<EMAIL>', $1, 'John Doe', 'employee', '550e8400-e29b-41d4-a716-446655440001'),
      ('550e8400-e29b-41d4-a716-446655440012', '<EMAIL>', $1, 'Jane Smith', 'employee', '550e8400-e29b-41d4-a716-446655440002')
      ON CONFLICT (email) DO NOTHING;
    `;
    await dataSource.query(employeeQuery, [employeePassword]);
    console.log('✅ Employee users seeded (password: employee123)');

    // Create sample products
    const productsQuery = `
      INSERT INTO products (id, name, description, price, stock_quantity, category) VALUES
      ('550e8400-e29b-41d4-a716-446655440020', 'Coffee Americano', 'Fresh brewed americano coffee', 25000, 100, 'Beverages'),
      ('550e8400-e29b-41d4-a716-446655440021', 'Cappuccino', 'Espresso with steamed milk foam', 35000, 100, 'Beverages'),
      ('550e8400-e29b-41d4-a716-446655440022', 'Croissant', 'Buttery flaky pastry', 15000, 50, 'Pastries'),
      ('550e8400-e29b-41d4-a716-446655440023', 'Sandwich Club', 'Triple layer club sandwich', 45000, 30, 'Food'),
      ('550e8400-e29b-41d4-a716-446655440024', 'Mineral Water', 'Fresh mineral water 500ml', 5000, 200, 'Beverages')
      ON CONFLICT (id) DO NOTHING;
    `;
    await dataSource.query(productsQuery);
    console.log('✅ Sample products seeded');

    // Create sample sales
    const salesQuery = `
      INSERT INTO sales (id, outlet_id, employee_id, subtotal, tax, discount, grand_total, payment_method, sale_date) VALUES
      ('550e8400-e29b-41d4-a716-446655440030', '550e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440011', 60000, 6600, 0, 66600, 'cash', NOW() - INTERVAL '1 day'),
      ('550e8400-e29b-41d4-a716-446655440031', '550e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440011', 80000, 8800, 5000, 83800, 'card', NOW() - INTERVAL '2 hours')
      ON CONFLICT (id) DO NOTHING;
    `;
    await dataSource.query(salesQuery);
    console.log('✅ Sample sales seeded');

    // Create sample sale items
    const saleItemsQuery = `
      INSERT INTO sale_items (sale_id, product_id, product_name, price, quantity, total) VALUES
      ('550e8400-e29b-41d4-a716-446655440030', '550e8400-e29b-41d4-a716-446655440021', 'Cappuccino', 35000, 1, 35000),
      ('550e8400-e29b-41d4-a716-446655440030', '550e8400-e29b-41d4-a716-446655440022', 'Croissant', 15000, 1, 15000),
      ('550e8400-e29b-41d4-a716-446655440030', '550e8400-e29b-41d4-a716-446655440024', 'Mineral Water', 5000, 2, 10000),
      ('550e8400-e29b-41d4-a716-446655440031', '550e8400-e29b-41d4-a716-446655440023', 'Sandwich Club', 45000, 1, 45000),
      ('550e8400-e29b-41d4-a716-446655440031', '550e8400-e29b-41d4-a716-446655440021', 'Cappuccino', 35000, 1, 35000)
      ON CONFLICT DO NOTHING;
    `;
    await dataSource.query(saleItemsQuery);
    console.log('✅ Sample sale items seeded');

    // Create sample attendance
    const attendanceQuery = `
      INSERT INTO attendances (employee_id, outlet_id, check_in_time, check_out_time) VALUES
      ('550e8400-e29b-41d4-a716-446655440011', '550e8400-e29b-41d4-a716-446655440001', NOW() - INTERVAL '8 hours', NOW() - INTERVAL '30 minutes'),
      ('550e8400-e29b-41d4-a716-446655440012', '550e8400-e29b-41d4-a716-446655440002', NOW() - INTERVAL '7 hours', NULL)
      ON CONFLICT DO NOTHING;
    `;
    await dataSource.query(attendanceQuery);
    console.log('✅ Sample attendance seeded');

    console.log('🎉 Database seeding completed successfully!');
    console.log('\n📋 Seeded Data Summary:');
    console.log('- 2 Outlets (Main Store, Branch Store)');
    console.log('- 1 Admin user (<EMAIL> / admin123)');
    console.log('- 2 Employee users (<EMAIL>, <EMAIL> / employee123)');
    console.log('- 5 Sample products');
    console.log('- 2 Sample sales with items');
    console.log('- 2 Sample attendance records');

  } catch (error) {
    console.error('❌ Error seeding database:', error);
  } finally {
    await dataSource.destroy();
  }
}

// Run the seed function
seed().catch((error) => {
  console.error('❌ Seed script failed:', error);
  process.exit(1);
});
