import 'dart:io';
import 'package:dio/dio.dart';

/// Quick test script to verify backend connectivity
/// Run with: dart run scripts/test_migration.dart
void main() async {
  print('🧪 Testing Backend Connectivity...\n');

  final dio = Dio(BaseOptions(
    baseUrl: 'http://localhost:3000/api/v1',
    connectTimeout: const Duration(seconds: 10),
    receiveTimeout: const Duration(seconds: 10),
  ));

  try {
    // Test 1: Health Check
    print('1. Testing health endpoint...');
    final healthResponse = await dio.get('/health');
    if (healthResponse.statusCode == 200) {
      print('   ✅ Health check passed');
    } else {
      print('   ❌ Health check failed');
      return;
    }

    // Test 2: Login Test
    print('\n2. Testing login...');
    final loginResponse = await dio.post('/auth/login', data: {
      'email': '<EMAIL>',
      'password': 'admin123',
    });

    if (loginResponse.statusCode == 200) {
      print('   ✅ Login successful');
      
      final accessToken = loginResponse.data['data']['accessToken'];
      print('   📝 Token received: ${accessToken.substring(0, 20)}...');

      // Test 3: Authenticated Request
      print('\n3. Testing authenticated request...');
      final profileResponse = await dio.get(
        '/auth/profile',
        options: Options(
          headers: {'Authorization': 'Bearer $accessToken'},
        ),
      );

      if (profileResponse.statusCode == 200) {
        print('   ✅ Profile request successful');
        final user = profileResponse.data['data'];
        print('   👤 User: ${user['name']} (${user['email']})');
      }

      // Test 4: Products Endpoint
      print('\n4. Testing products endpoint...');
      final productsResponse = await dio.get(
        '/products',
        options: Options(
          headers: {'Authorization': 'Bearer $accessToken'},
        ),
      );

      if (productsResponse.statusCode == 200) {
        print('   ✅ Products request successful');
        final products = productsResponse.data['data']['data'] as List;
        print('   📦 Found ${products.length} products');
      }

      // Test 5: Sales Endpoint
      print('\n5. Testing sales endpoint...');
      final salesResponse = await dio.get(
        '/sales',
        options: Options(
          headers: {'Authorization': 'Bearer $accessToken'},
        ),
      );

      if (salesResponse.statusCode == 200) {
        print('   ✅ Sales request successful');
        final sales = salesResponse.data['data']['data'] as List;
        print('   💰 Found ${sales.length} sales');
      }

      print('\n🎉 All tests passed! Backend is ready for Flutter migration.');
      print('\n📋 Next steps:');
      print('   1. Update Flutter screens to use HTTP services');
      print('   2. Test offline functionality');
      print('   3. Implement real-time WebSocket features');
      print('   4. Migrate existing data');

    } else {
      print('   ❌ Login failed');
    }

  } catch (e) {
    print('❌ Test failed: $e');
    print('\n🔧 Troubleshooting:');
    print('   1. Make sure backend server is running: cd pos-backend && npm run start:dev');
    print('   2. Check if database is seeded: cd pos-backend && npm run seed');
    print('   3. Verify API URL: http://localhost:3000/api/v1');
    exit(1);
  }
}
