name: pos_app
description: A POS and Outlet Management Application with offline capabilities.
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  # HTTP & API
  dio: ^5.4.0
  retrofit: ^4.0.3
  json_annotation: ^4.8.1

  # WebSocket for real-time
  socket_io_client: ^2.0.3

  # JWT & Auth
  jwt_decoder: ^2.0.1
  shared_preferences: ^2.2.2

  # State Management & DI
  flutter_riverpod: ^2.6.1
  hooks_riverpod: ^2.6.1
  flutter_hooks: ^0.21.2

  # Local Storage (Offline)
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # UI & Styling
  google_fonts: ^6.2.1
  flutter_svg: ^2.1.0
  cached_network_image: ^3.4.1
  shimmer: ^3.0.0
  intl: ^0.20.2

  # Location
  geolocator: ^14.0.0
  geocoding: ^2.1.0

  # Utilities
  uuid: ^4.5.1
  path_provider: ^2.1.0
  internet_connection_checker: ^3.0.1
  equatable: ^2.0.5
  dartz: ^0.10.1
  logger: ^2.5.0

  # Icons
  cupertino_icons: ^1.0.5
  font_awesome_flutter: ^10.5.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  build_runner: ^2.4.6
  hive_generator: ^2.0.0
  flutter_launcher_icons: ^0.13.1
  mockito: ^5.4.2

  # Code generation for HTTP client
  retrofit_generator: ^8.0.6
  json_serializable: ^6.7.1

flutter:
  uses-material-design: true
  assets:
    - assets/images/
    - assets/icons/