name: pos_app
description: A POS and Outlet Management Application with offline capabilities.
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  
  # Firebase
  firebase_core: ^3.13.0
  firebase_auth: ^5.5.3
  cloud_firestore: ^5.6.7
  firebase_storage: ^12.4.5
  
  # State Management & DI
  flutter_riverpod: ^2.6.1
  hooks_riverpod: ^2.6.1
  flutter_hooks: ^0.21.2
  
  # Local Storage (Offline)
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  
  # UI & Styling
  google_fonts: ^6.2.1
  flutter_svg: ^2.1.0
  cached_network_image: ^3.4.1
  shimmer: ^3.0.0
  intl: ^0.20.2
  
  # Location
  geolocator: ^14.0.0
  geocoding: ^2.1.0
  
  # Utilities
  uuid: ^4.5.1
  path_provider: ^2.1.0
  internet_connection_checker: ^3.0.1
  equatable: ^2.0.5
  dartz: ^0.10.1
  logger: ^2.5.0
  
  # Icons
  cupertino_icons: ^1.0.5
  font_awesome_flutter: ^10.5.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  build_runner: ^2.4.6
  hive_generator: ^2.0.0
  flutter_launcher_icons: ^0.13.1
  mockito: ^5.4.2

flutter:
  uses-material-design: true
  assets:
    - assets/images/
    - assets/icons/