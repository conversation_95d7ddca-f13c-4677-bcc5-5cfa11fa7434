// seed_firestore.js

const { initializeApp, applicationDefault, cert } = require("firebase-admin/app");
const { getFirestore, GeoPoint } = require("firebase-admin/firestore");
const { getAuth } = require("firebase-admin/auth");
// const admin = require("firebase-admin");
// const { v4: uuidv4 } = require("uuid");

const serviceAccount = require("../serviceAccountKey.json");

initializeApp({
  credential: cert(serviceAccount),
});

const db = getFirestore();
const auth = getAuth();

const AppConstants = {
  usersCollection: "users",
  outletsCollection: "outlets",
  productsCollection: "products",
};

async function seedDatabase() {
  console.info("Starting database seeding...");
  const adminUser = await createAdminUser();
  console.info("Admin user created:", adminUser.email);

  const outlets = await createOutlets();
  console.info("Outlets created:", outlets.length);

  for (const outlet of outlets) {
    const employees = await createEmployees(outlet);
    console.info(`Employees created for ${outlet.name}:`, employees.length);
  }

  const products = await createProducts(outlets);
  console.info("Products created:", products.length);
  console.info("Database seeding completed successfully.");
}

async function createAdminUser() {
  const email = "<EMAIL>";
  const password = "admin@123";

  const userRecord = await auth.createUser({
    email,
    password,
  });

  const user = {
    id: userRecord.uid,
    name: "Admin",
    email,
    phone: "+6281234567890",
    role: "admin",
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  await db.collection(AppConstants.usersCollection).doc(user.id).set(user);
  return user;
}

async function createOutlets() {
  const outlets = [
    {
      id: "outlet1",
      name: "Main Store",
      address: "Jl. Sudirman No. 123, Jakarta",
      location: new GeoPoint(-6.2088, 106.8456),
      phone: "+6281234567891",
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: "outlet2",
      name: "Branch Store",
      address: "Jl. Thamrin No. 456, Jakarta",
      location: new GeoPoint(-6.2000, 106.8200),
      phone: "+6281234567892",
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ];

  for (const outlet of outlets) {
    await db.collection(AppConstants.outletsCollection).doc(outlet.id).set(outlet);
  }

  return outlets;
}

async function createEmployees(outlet) {
  const employees = [
    {
      name: `Fulan A${outlet.id}`,
      email: `fulan_a@${outlet.id}.mudahkan.com`,
      phone: "+6281234567893",
      password: "fulan_a",
    },
    {
      name: `Fulan B${outlet.id}`,
      email: `fulan_b@${outlet.id}.mudahkan.com`,
      phone: "+6281234567894",
      password: "fulan_b",
    },
  ];

  const createdEmployees = [];

  for (const employee of employees) {
    const userRecord = await auth.createUser({
      email: employee.email,
      password: employee.password,
    });

    const user = {
      id: userRecord.uid,
      name: employee.name,
      email: employee.email,
      phone: employee.phone,
      role: "employee",
      outletId: outlet.id,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    console.info(`Employee created :`,employee.email,employee.name);

    await db.collection(AppConstants.usersCollection).doc(user.id).set(user);
    createdEmployees.push(user);
  }

  return createdEmployees;
}

async function createProducts(outlets) {
  const products = [
    {
      id: "product-1",
      name: "Coffee Latte",
      description: "Smooth and creamy coffee with steamed milk",
      price: 25000.0,
      category: "Beverages",
      stock: Object.fromEntries(outlets.map(o => [o.id, 50])),
    },
    {
      id: "product-2",
      name: "Cappuccino",
      description: "Classic Italian coffee with foamed milk",
      price: 28000.0,
      category: "Beverages",
      stock: Object.fromEntries(outlets.map(o => [o.id, 50])),
    },
    {
      id: "product-3",
      name: "Croissant",
      description: "Buttery and flaky French pastry",
      price: 15000.0,
      category: "Pastries",
      stock: Object.fromEntries(outlets.map(o => [o.id, 30])),
    },
    {
      id: "product-4",
      name: "Chocolate Cake",
      description: "Rich and moist chocolate cake",
      price: 35000.0,
      category: "Cakes",
      stock: Object.fromEntries(outlets.map(o => [o.id, 20])),
    },
  ];

  const createdProducts = [];

  for (const product of products) {
    const newProduct = {
      ...product,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    await db.collection(AppConstants.productsCollection).doc(newProduct.id).set(newProduct);
    createdProducts.push(newProduct);
  }

  return createdProducts;
}

seedDatabase().catch(console.error);
