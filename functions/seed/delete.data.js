const admin = require("firebase-admin");
const { cert } = require("firebase-admin/app");
const serviceAccount = require("../serviceAccountKey.json");

admin.initializeApp({
  credential: cert(serviceAccount),
});

const db = admin.firestore();
const auth = admin.auth();

const AppConstants = {
  usersCollection: "users",
  outletsCollection: "outlets",
  productsCollection: "products",
};

async function deleteCollection(collectionPath, batchSize = 500) {
  const collectionRef = db.collection(collectionPath);
  const query = collectionRef.limit(batchSize);

  return new Promise((resolve, reject) => {
    deleteQueryBatch(query, resolve).catch(reject);
  });
}

async function deleteQueryBatch(query, resolve) {
  const snapshot = await query.get();

  if (snapshot.empty) {
    resolve();
    return;
  }

  const batch = db.batch();
  snapshot.docs.forEach((doc) => {
    batch.delete(doc.ref);
  });

  await batch.commit();

  process.nextTick(() => {
    deleteQueryBatch(query, resolve);
  });
}

async function deleteAuthUsersFromFirestoreUsers() {
  console.info("Fetching user UIDs from Firestore...");

  const snapshot = await db.collection(AppConstants.usersCollection).get();
  if (snapshot.empty) {
    console.info("No users found in Firestore.");
    return;
  }

  const uids = snapshot.docs.map(doc => doc.id);

  console.info(`Deleting ${uids.length} user(s) from Firebase Auth...`);

  const BATCH_SIZE = 1000;
  for (let i = 0; i < uids.length; i += BATCH_SIZE) {
    const batch = uids.slice(i, i + BATCH_SIZE);
    const result = await auth.deleteUsers(batch);
    console.info(`Deleted ${result.successCount} Auth user(s), ${result.failureCount} failed.`);
    if (result.errors.length > 0) {
      result.errors.forEach(err => {
        console.error(`Failed to delete user ${err.index}: ${err.message}`);
      });
    }
  }
}

async function truncateAllCollectionsAndAuth() {
  console.info("Starting truncate process...");

  await deleteAuthUsersFromFirestoreUsers();

  for (const collectionName of Object.values(AppConstants)) {
    console.info(`Truncating collection: ${collectionName}`);
    await deleteCollection(collectionName);
    console.info(`Collection ${collectionName} truncated.`);
  }

  console.info("All collections and Auth users truncated successfully.");
}

truncateAllCollectionsAndAuth()
  .then(() => {
    console.info("✅ Truncate finished.");
    process.exit(0);
  })
  .catch((error) => {
    console.error("❌ Error during truncate process:", error);
    process.exit(1);
  });
