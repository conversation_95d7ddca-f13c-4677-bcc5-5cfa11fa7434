{"name": "functions", "description": "Cloud Functions for Firebase", "scripts": {"lint": "eslint .", "serve": "firebase emulators:start --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "deployfs:rules": "firebase deploy --only firestore:rules", "deployfs:indexes": "firebase deploy --only firestore:indexes", "logs": "firebase functions:log"}, "engines": {"node": "22"}, "main": "index.js", "dependencies": {"firebase-admin": "^13.4.0", "firebase-functions": "^6.3.2", "uuid": "^11.1.0"}, "devDependencies": {"eslint": "^8.57.0", "eslint-config-google": "^0.14.0", "firebase-functions-test": "^3.4.1"}, "private": true}