rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isAdmin() {
      return isAuthenticated() && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    function isEmployee() {
      return isAuthenticated() && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'employee';
    }
    
    function belongsToOutlet(outletId) {
      return get(/databases/$(database)/documents/users/$(request.auth.uid)).data.outletId == outletId;
    }
    
    function isOwner(userId) {
      return request.auth.uid == userId;
    }
    
    // Users Collection
    match /users/{userId} {
      allow read: if isAuthenticated() && (isAdmin() || isOwner(userId));
      allow create, update, delete: if isAdmin();
    }
    
    // Outlets Collection
    match /outlets/{outletId} {
      allow read: if isAuthenticated();
      allow create, update, delete: if isAdmin();
    }
    
    // Products Collection
    match /products/{productId} {
      allow read: if isAuthenticated();
      allow create, update, delete: if isAdmin();
      
      // Allow employees to update stock for their outlet only
      allow update: if isEmployee() && 
        request.resource.data.diff(resource.data).affectedKeys().hasOnly(['stock']) &&
        request.resource.data.stock.diff(resource.data.stock).affectedKeys().hasOnly([get(/databases/$(database)/documents/users/$(request.auth.uid)).data.outletId]);
    }
    
    // Sales Collection
    match /sales/{saleId} {
      // Admins can read all sales
      allow read: if isAdmin();
      
      // Employees can read sales from their outlet
      allow read: if isEmployee() && belongsToOutlet(resource.data.outletId);
      
      // Allow create if user is authenticated and sale belongs to their outlet
      allow create: if isAuthenticated() && 
        (isAdmin() || belongsToOutlet(request.resource.data.outletId)) &&
        request.resource.data.employeeId == request.auth.uid;
        
      // No updates or deletes allowed to maintain data integrity
      allow update, delete: if false;
    }
    
    // Attendance Collection
    match /attendance/{attendanceId} {
      // Admins can read all attendance records
      allow read: if isAdmin();
      
      // Employees can read their own attendance records
      allow read: if isEmployee() && resource.data.employeeId == request.auth.uid;
      
      // Allow create if record is for the authenticated user
      allow create: if isAuthenticated() && 
        request.resource.data.employeeId == request.auth.uid &&
        (isAdmin() || belongsToOutlet(request.resource.data.outletId));
      
      // Allow update only for clock out
      allow update: if isAuthenticated() && 
        resource.data.employeeId == request.auth.uid &&
        request.resource.data.diff(resource.data).affectedKeys().hasOnly(['clockOutTime', 'clockOutLocation']);
        
      // No deletes allowed to maintain attendance records
      allow delete: if false;
    }
  }
}