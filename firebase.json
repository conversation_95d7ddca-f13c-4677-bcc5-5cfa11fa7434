{"flutter": {"platforms": {"android": {"default": {"projectId": "mudahkan-pos-app", "appId": "1:339402194886:android:5c42083a37da4a9638fcc7", "fileOutput": "android/app/google-services.json"}}, "dart": {"lib/firebase_options.dart": {"projectId": "mudahkan-pos-app", "configurations": {"android": "1:339402194886:android:5c42083a37da4a9638fcc7", "ios": "1:339402194886:ios:992cf3c5a7eaf22238fcc7", "macos": "1:339402194886:ios:992cf3c5a7eaf22238fcc7", "web": "1:339402194886:web:1fe698f528d89bdb38fcc7"}}}, "ios": {"default": {"projectId": "mudahkan-pos-app", "appId": "1:339402194886:ios:992cf3c5a7eaf22238fcc7", "uploadDebugSymbols": false, "fileOutput": "ios/Runner/GoogleService-Info.plist"}}, "macos": {"default": {"projectId": "mudahkan-pos-app", "appId": "1:339402194886:ios:992cf3c5a7eaf22238fcc7", "uploadDebugSymbols": false, "fileOutput": "macos/Runner/GoogleService-Info.plist"}}}}, "functions": [{"source": "functions", "codebase": "default", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log", "*.local"], "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run lint"]}]}