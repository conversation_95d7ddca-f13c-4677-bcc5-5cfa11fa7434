# 🔄 Firebase to NestJS Backend Migration Guide

## 📋 Overview

This guide outlines the migration from Firebase backend to a self-hosted NestJS backend for the POS application.

## 🎯 Migration Status

### ✅ Phase 1: Backend Setup (COMPLETED)
- [x] NestJS backend with TypeScript
- [x] PostgreSQL database with TypeORM
- [x] Redis caching
- [x] JWT authentication
- [x] Complete API endpoints
- [x] WebSocket real-time features
- [x] Docker setup
- [x] Database seeding

### 🔄 Phase 2: Flutter App Migration (IN PROGRESS)
- [x] Remove Firebase dependencies
- [x] Add HTTP client dependencies (Dio, Retrofit)
- [x] Create HTTP client service
- [x] Create new auth service (HTTP-based)
- [x] Create new repositories (HTTP-based)
- [x] WebSocket service for real-time
- [ ] Update all screens to use new services
- [ ] Test offline functionality
- [ ] Update state management

### 📋 Phase 3: Data Migration (PENDING)
- [ ] Export data from Firestore
- [ ] Transform data format
- [ ] Import to PostgreSQL
- [ ] Verify data integrity

## 🔧 Technical Changes

### Dependencies Changes

**Removed:**
```yaml
# Firebase dependencies (removed)
firebase_core: ^3.13.0
firebase_auth: ^5.5.3
cloud_firestore: ^5.6.7
firebase_storage: ^12.4.5
```

**Added:**
```yaml
# HTTP & API
dio: ^5.4.0
retrofit: ^4.0.3
json_annotation: ^4.8.1

# WebSocket for real-time
socket_io_client: ^2.0.3

# JWT & Auth
jwt_decoder: ^2.0.1
shared_preferences: ^2.2.2

# Code generation
retrofit_generator: ^8.0.6
json_serializable: ^6.7.1
```

### Architecture Changes

#### Before (Firebase):
```
Flutter App
    ↓
Firebase Auth
    ↓
Cloud Firestore
    ↓
Firebase Storage
```

#### After (NestJS):
```
Flutter App
    ↓
HTTP Client (Dio)
    ↓
NestJS Backend API
    ↓
PostgreSQL + Redis
```

### New Services Created

1. **HttpClientService** - Handles all HTTP requests with automatic token management
2. **AuthServiceHttp** - JWT-based authentication
3. **POSRepositoryHttp** - Sales management with offline support
4. **ProductRepositoryHttp** - Product management
5. **WebSocketService** - Real-time updates

## 🚀 Getting Started

### 1. Start Backend Server

```bash
cd pos-backend
npm run setup
```

This will:
- Install dependencies
- Setup database
- Seed sample data
- Start development server

### 2. Update Flutter App

```bash
flutter pub get
flutter run
```

### 3. Test API Connection

```bash
cd pos-backend
npm run test:api
```

## 🔐 Authentication Changes

### Before (Firebase Auth):
```dart
// Firebase sign in
final credential = await FirebaseAuth.instance.signInWithEmailAndPassword(
  email: email,
  password: password,
);
```

### After (HTTP Auth):
```dart
// HTTP sign in
final result = await authService.signIn(email, password);
result.fold(
  (failure) => handleError(failure),
  (user) => handleSuccess(user),
);
```

## 💾 Data Storage Changes

### Before (Firestore):
```dart
// Firestore save
await FirebaseFirestore.instance
    .collection('sales')
    .add(sale.toMap());
```

### After (HTTP + Offline):
```dart
// HTTP save with offline support
final result = await posRepository.createSale(sale);
result.fold(
  (failure) => handleError(failure),
  (savedSale) => handleSuccess(savedSale),
);
```

## 🌐 Real-time Updates

### Before (Firestore Streams):
```dart
// Firestore stream
FirebaseFirestore.instance
    .collection('sales')
    .snapshots()
    .listen((snapshot) {
      // Handle updates
    });
```

### After (WebSocket):
```dart
// WebSocket stream
ref.listen(newSaleStreamProvider, (previous, next) {
  next.when(
    data: (sale) => handleNewSale(sale),
    loading: () => {},
    error: (error, stack) => handleError(error),
  );
});
```

## 📱 Screen Updates Required

### Priority 1 (Critical):
- [ ] Login Screen
- [ ] Sales Screen
- [ ] Product Selection
- [ ] Checkout Process

### Priority 2 (Important):
- [ ] Dashboard
- [ ] Reports
- [ ] Settings
- [ ] User Management

### Priority 3 (Nice to have):
- [ ] Attendance
- [ ] Inventory Management
- [ ] Analytics

## 🧪 Testing Strategy

### 1. Unit Tests
```bash
flutter test
```

### 2. Integration Tests
```bash
flutter test integration_test/
```

### 3. API Tests
```bash
cd pos-backend
npm run test:api
```

### 4. Manual Testing Checklist
- [ ] Login/Logout
- [ ] Create sale online
- [ ] Create sale offline
- [ ] Sync offline sales
- [ ] Real-time updates
- [ ] Product search
- [ ] Reports generation

## 🔄 Offline Functionality

### Sales Offline Support:
1. **Online**: Direct HTTP call to backend
2. **Offline**: Save to Hive local storage
3. **Sync**: Batch upload when connection restored

### Implementation:
```dart
// Automatic offline handling
final result = await posRepository.createSale(sale);
// Will save offline if no internet, sync later
```

## 📊 Data Migration Process

### 1. Export from Firebase
```bash
# Use Firebase Admin SDK to export data
node scripts/export-firestore.js
```

### 2. Transform Data
```bash
# Convert Firebase format to PostgreSQL format
node scripts/transform-data.js
```

### 3. Import to PostgreSQL
```bash
# Import using backend seeding
npm run seed:production
```

## 🚨 Troubleshooting

### Common Issues:

1. **Connection Refused**
   - Ensure backend server is running
   - Check API_CONFIG.baseUrl

2. **Authentication Failed**
   - Verify JWT tokens
   - Check token expiration

3. **Offline Sync Issues**
   - Check Hive storage
   - Verify network connectivity

4. **WebSocket Connection Failed**
   - Check WebSocket URL
   - Verify authentication

### Debug Commands:

```bash
# Check backend logs
cd pos-backend && npm run docker:logs

# Check Flutter logs
flutter logs

# Test API endpoints
cd pos-backend && npm run test:api
```

## 📈 Performance Improvements

### Backend Optimizations:
- Database indexing
- Redis caching
- Connection pooling
- Query optimization

### Flutter Optimizations:
- HTTP request caching
- Lazy loading
- Image optimization
- State management optimization

## 🔒 Security Considerations

### Backend Security:
- JWT token validation
- Rate limiting
- Input validation
- CORS configuration
- SQL injection prevention

### Flutter Security:
- Secure token storage
- Certificate pinning
- API key protection
- Biometric authentication

## 📚 API Documentation

### Available Endpoints:
- **Swagger UI**: http://localhost:3000/docs
- **Base URL**: http://localhost:3000/api/v1

### Sample Credentials:
- **Admin**: <EMAIL> / admin123
- **Employee**: <EMAIL> / employee123

## 🎯 Next Steps

1. **Complete Screen Updates** - Update remaining screens to use HTTP services
2. **Implement Data Migration** - Export and migrate existing data
3. **Production Deployment** - Deploy backend to production server
4. **Performance Testing** - Load testing and optimization
5. **User Training** - Train users on new features

## 📞 Support

For issues or questions:
1. Check this migration guide
2. Review API documentation
3. Check backend logs
4. Test with provided scripts

## 🎉 Benefits After Migration

✅ **Full Control** - Complete control over backend infrastructure
✅ **Cost Effective** - No Firebase pricing concerns
✅ **Better Performance** - Optimized for POS use case
✅ **Offline First** - Robust offline functionality
✅ **Real-time Updates** - WebSocket-based real-time features
✅ **Scalability** - Easy to scale and customize
✅ **Data Ownership** - Complete data ownership and privacy
