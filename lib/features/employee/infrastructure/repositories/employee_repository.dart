import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dartz/dartz.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:pos_app/core/constants/app_constants.dart';
import 'package:pos_app/core/errors/failures.dart';
import 'package:pos_app/core/utils/logger.dart';
import 'package:pos_app/core/utils/network_info.dart';
import 'package:pos_app/models/user.dart' as app_user;

abstract class EmployeeRepository {
  Future<Either<Failure, List<app_user.User>>> getEmployees();
  Future<Either<Failure, app_user.User>> createEmployee(
    app_user.User employee,
    String password,
  );
  Future<Either<Failure, app_user.User>> updateEmployee(app_user.User employee);
  Future<Either<Failure, void>> deleteEmployee(String id);
}

class EmployeeRepositoryImpl implements EmployeeRepository {
  final FirebaseFirestore _firestore;
  final firebase_auth.FirebaseAuth _auth;
  final NetworkInfo _networkInfo;

  EmployeeRepositoryImpl({
    required FirebaseFirestore firestore,
    required firebase_auth.FirebaseAuth auth,
    required NetworkInfo networkInfo,
  })  : _firestore = firestore,
        _auth = auth,
        _networkInfo = networkInfo;

  @override
  Future<Either<Failure, List<app_user.User>>> getEmployees() async {
    try {
      final isConnected = await _networkInfo.isConnected;
      if (!isConnected) {
        return const Left(NetworkFailure(AppConstants.networkErrorMessage));
      }

      final querySnapshot = await _firestore
          .collection(AppConstants.usersCollection)
          .where('role', isEqualTo: app_user.UserRole.employee.toString().split('.').last)
          .orderBy('name')
          .get();

      final employees = querySnapshot.docs
          .map((doc) => app_user.User.fromMap(doc.data()))
          .toList();

      return Right(employees);
    } catch (e) {
      AppLogger.error('Get employees error', e);
      return Left(UnknownFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, app_user.User>> createEmployee(
    app_user.User employee,
    String password,
  ) async {
    try {
      final isConnected = await _networkInfo.isConnected;
      if (!isConnected) {
        return const Left(NetworkFailure(AppConstants.networkErrorMessage));
      }

      // Create Firebase Auth user
      final userCredential = await _auth.createUserWithEmailAndPassword(
        email: employee.email,
        password: password,
      );

      if (userCredential.user == null) {
        return const Left(AuthFailure('Failed to create user account'));
      }

      // Create Firestore user document
      final newEmployee = employee.copyWith(
        id: userCredential.user!.uid,
        role: app_user.UserRole.employee,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(newEmployee.id)
          .set(newEmployee.toMap());

      return Right(newEmployee);
    } on firebase_auth.FirebaseAuthException catch (e) {
      AppLogger.error('Create employee auth error', e);
      switch (e.code) {
        case 'email-already-in-use':
          return const Left(
            ValidationFailure('Email is already registered'),
          );
        case 'invalid-email':
          return const Left(
            ValidationFailure('Invalid email address'),
          );
        case 'weak-password':
          return const Left(
            ValidationFailure('Password is too weak'),
          );
        default:
          return Left(AuthFailure(e.message ?? AppConstants.unknownErrorMessage));
      }
    } catch (e) {
      AppLogger.error('Create employee error', e);
      return Left(UnknownFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, app_user.User>> updateEmployee(
    app_user.User employee,
  ) async {
    try {
      final isConnected = await _networkInfo.isConnected;
      if (!isConnected) {
        return const Left(NetworkFailure(AppConstants.networkErrorMessage));
      }

      final updatedEmployee = employee.copyWith(
        updatedAt: DateTime.now(),
      );

      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(employee.id)
          .update(updatedEmployee.toMap());

      return Right(updatedEmployee);
    } catch (e) {
      AppLogger.error('Update employee error', e);
      return Left(UnknownFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> deleteEmployee(String id) async {
    try {
      final isConnected = await _networkInfo.isConnected;
      if (!isConnected) {
        return const Left(NetworkFailure(AppConstants.networkErrorMessage));
      }

      // Delete from Firestore
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(id)
          .delete();

      // Delete from Firebase Auth
      final user = await _auth.currentUser;
      if (user?.uid == id) {
        await user?.delete();
      }

      return const Right(null);
    } catch (e) {
      AppLogger.error('Delete employee error', e);
      return Left(UnknownFailure(e.toString()));
    }
  }
}