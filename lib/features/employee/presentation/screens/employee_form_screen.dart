import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:pos_app/core/config/app_theme.dart';
import 'package:pos_app/core/constants/app_constants.dart';
import 'package:pos_app/core/widgets/app_button.dart';
import 'package:pos_app/features/employee/application/notifiers/employee_notifier.dart';
import 'package:pos_app/features/employee/application/providers/employee_providers.dart';
import 'package:pos_app/features/outlet/application/providers/outlet_providers.dart';
import 'package:pos_app/models/user.dart';

class EmployeeFormScreen extends HookConsumerWidget {
  static const String routeName = '/employee-form';

  final User? employee;

  const EmployeeFormScreen({
    super.key,
    this.employee,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = useMemoized(() => GlobalKey<FormState>());
    final nameController = useTextEditingController(text: employee?.name);
    final emailController = useTextEditingController(text: employee?.email);
    final phoneController = useTextEditingController(text: employee?.phone);
    final passwordController = useTextEditingController();
    final confirmPasswordController = useTextEditingController();
    final selectedOutletId = useState<String?>(employee?.outletId);
    final obscurePassword = useState(true);
    final obscureConfirmPassword = useState(true);

    // Load outlets
    useEffect(() {
      ref.read(outletStateProvider.notifier).getOutlets();
      return null;
    }, const []);

    // Watch form state
    final formState = ref.watch(employeeFormStateProvider);
    final outletState = ref.watch(outletStateProvider);

    // Handle form state changes
    ref.listen(employeeFormStateProvider, (previous, next) {
      if (next is EmployeeFormSuccess) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(next.message),
            backgroundColor: AppTheme.successColor,
          ),
        );
        Navigator.pop(context);
      } else if (next is EmployeeFormError) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(next.message),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    });

    // Handle form submission
    Future<void> handleSubmit() async {
      if (formKey.currentState?.validate() ?? false) {
        final newEmployee = (employee ?? User.empty()).copyWith(
          name: nameController.text,
          email: emailController.text,
          phone: phoneController.text,
          outletId: selectedOutletId.value,
        );

        if (employee == null) {
          await ref.read(employeeFormStateProvider.notifier).createEmployee(
                newEmployee,
                passwordController.text,
              );
        } else {
          await ref.read(employeeFormStateProvider.notifier).updateEmployee(
                newEmployee,
              );
        }
      }
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(employee == null ? 'Add Employee' : 'Edit Employee'),
      ),
      body: Form(
        key: formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            TextFormField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'Full Name',
                hintText: 'Enter employee name',
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return AppConstants.requiredFieldMessage;
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: emailController,
              decoration: const InputDecoration(
                labelText: 'Email',
                hintText: 'Enter employee email',
              ),
              keyboardType: TextInputType.emailAddress,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return AppConstants.requiredFieldMessage;
                }
                if (!RegExp(r'^[^@]+@[^@]+\.[^@]+$').hasMatch(value)) {
                  return AppConstants.invalidEmailMessage;
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: phoneController,
              decoration: const InputDecoration(
                labelText: 'Phone',
                hintText: 'Enter employee phone',
              ),
              keyboardType: TextInputType.phone,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return AppConstants.requiredFieldMessage;
                }
                if (!RegExp(r'^\+?[\d\s-]+$').hasMatch(value)) {
                  return AppConstants.invalidPhoneMessage;
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            if (employee == null) ...[
              TextFormField(
                controller: passwordController,
                obscureText: obscurePassword.value,
                decoration: InputDecoration(
                  labelText: 'Password',
                  hintText: 'Enter password',
                  suffixIcon: IconButton(
                    icon: Icon(
                      obscurePassword.value
                          ? Icons.visibility_outlined
                          : Icons.visibility_off_outlined,
                    ),
                    onPressed: () {
                      obscurePassword.value = !obscurePassword.value;
                    },
                  ),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return AppConstants.requiredFieldMessage;
                  }
                  if (value.length < 6) {
                    return AppConstants.passwordMinLengthMessage;
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: confirmPasswordController,
                obscureText: obscureConfirmPassword.value,
                decoration: InputDecoration(
                  labelText: 'Confirm Password',
                  hintText: 'Confirm password',
                  suffixIcon: IconButton(
                    icon: Icon(
                      obscureConfirmPassword.value
                          ? Icons.visibility_outlined
                          : Icons.visibility_off_outlined,
                    ),
                    onPressed: () {
                      obscureConfirmPassword.value = !obscureConfirmPassword.value;
                    },
                  ),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return AppConstants.requiredFieldMessage;
                  }
                  if (value != passwordController.text) {
                    return 'Passwords do not match';
                  }
                  return null;
                },
              ),
            ],
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: selectedOutletId.value,
              decoration: const InputDecoration(
                labelText: 'Assign Outlet',
                hintText: 'Select outlet',
              ),
              items: outletState.maybeWhen(
                success: (outletSuccess) => outletSuccess.outlets.map((outlet) {
                  return DropdownMenuItem(
                    value: outlet.id,
                    child: Text(outlet.name),
                  );
                }).toList(),
                orElse: () => [],
              ),
              onChanged: (value) {
                selectedOutletId.value = value;
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return AppConstants.requiredFieldMessage;
                }
                return null;
              },
            ),
            const SizedBox(height: 24),
            AppButton(
              label: employee == null ? 'Create Employee' : 'Update Employee',
              onPressed: handleSubmit,
              isLoading: formState is EmployeeFormLoading,
              icon: employee == null ? Icons.add : Icons.save,
            ),
          ],
        ),
      ),
    );
  }
}