import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:pos_app/features/auth/application/providers/auth_providers.dart';
import 'package:pos_app/features/employee/application/notifiers/employee_notifier.dart';
import 'package:pos_app/features/employee/infrastructure/repositories/employee_repository.dart';

// Repository Provider
final employeeRepositoryProvider = Provider<EmployeeRepository>((ref) {
  return EmployeeRepositoryImpl(
    firestore: FirebaseFirestore.instance,
    auth: FirebaseAuth.instance,
    networkInfo: ref.watch(networkInfoProvider),
  );
});

// Notifier Providers
final employeeStateProvider = StateNotifierProvider<EmployeeNotifier, EmployeeState>(
  (ref) => EmployeeNotifier(ref.watch(employeeRepositoryProvider)),
);

final employeeFormStateProvider = StateNotifierProvider<EmployeeFormNotifier, EmployeeFormState>(
  (ref) => EmployeeFormNotifier(ref.watch(employeeRepositoryProvider)),
);