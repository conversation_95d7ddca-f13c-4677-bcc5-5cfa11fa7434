import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:pos_app/features/auth/application/providers/auth_providers.dart';
import 'package:pos_app/features/employee/application/notifiers/employee_notifier.dart';
import 'package:pos_app/features/employee/infrastructure/repositories/employee_repository.dart';

// Repository Provider (Placeholder - will be implemented later)
final employeeRepositoryProvider = Provider<EmployeeRepository>((ref) {
  // TODO: Implement HTTP-based employee repository
  throw UnimplementedError('Employee repository not yet migrated to HTTP');
});

// Notifier Providers
final employeeStateProvider = StateNotifierProvider<EmployeeNotifier, EmployeeState>(
  (ref) => EmployeeNotifier(ref.watch(employeeRepositoryProvider)),
);

final employeeFormStateProvider = StateNotifierProvider<EmployeeFormNotifier, EmployeeFormState>(
  (ref) => EmployeeFormNotifier(ref.watch(employeeRepositoryProvider)),
);