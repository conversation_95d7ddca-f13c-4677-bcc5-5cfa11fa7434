import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:pos_app/core/utils/logger.dart';
import 'package:pos_app/features/employee/infrastructure/repositories/employee_repository.dart';
import 'package:pos_app/models/user.dart';

// Employee State
abstract class EmployeeState {
  const EmployeeState();
}

class EmployeeInitial extends EmployeeState {
  const EmployeeInitial();
}

class EmployeeLoading extends EmployeeState {
  const EmployeeLoading();
}

class EmployeeSuccess extends EmployeeState {
  final List<User> employees;
  const EmployeeSuccess(this.employees);
}

class EmployeeError extends EmployeeState {
  final String message;
  const EmployeeError(this.message);
}

// Employee Form State
abstract class EmployeeFormState {
  const EmployeeFormState();
}

class EmployeeFormInitial extends EmployeeFormState {
  const EmployeeFormInitial();
}

class EmployeeFormLoading extends EmployeeFormState {
  const EmployeeFormLoading();
}

class EmployeeFormSuccess extends EmployeeFormState {
  final String message;
  const EmployeeFormSuccess(this.message);
}

class EmployeeFormError extends EmployeeFormState {
  final String message;
  const EmployeeFormError(this.message);
}

// Employee Notifier
class EmployeeNotifier extends StateNotifier<EmployeeState> {
  final EmployeeRepository _repository;

  EmployeeNotifier(this._repository) : super(const EmployeeInitial());

  Future<void> getEmployees() async {
    state = const EmployeeLoading();
    final result = await _repository.getEmployees();
    result.fold(
      (failure) {
        AppLogger.error('Get employees failed: ${failure.message}');
        state = EmployeeError(failure.message);
      },
      (employees) {
        AppLogger.info('Get employees success: ${employees.length} employees');
        state = EmployeeSuccess(employees);
      },
    );
  }
}

// Employee Form Notifier
class EmployeeFormNotifier extends StateNotifier<EmployeeFormState> {
  final EmployeeRepository _repository;

  EmployeeFormNotifier(this._repository) : super(const EmployeeFormInitial());

  Future<void> createEmployee(User employee, String password) async {
    state = const EmployeeFormLoading();
    final result = await _repository.createEmployee(employee, password);
    result.fold(
      (failure) {
        AppLogger.error('Create employee failed: ${failure.message}');
        state = EmployeeFormError(failure.message);
      },
      (employee) {
        AppLogger.info('Create employee success: ${employee.name}');
        state = const EmployeeFormSuccess('Employee created successfully');
      },
    );
  }

  Future<void> updateEmployee(User employee) async {
    state = const EmployeeFormLoading();
    final result = await _repository.updateEmployee(employee);
    result.fold(
      (failure) {
        AppLogger.error('Update employee failed: ${failure.message}');
        state = EmployeeFormError(failure.message);
      },
      (employee) {
        AppLogger.info('Update employee success: ${employee.name}');
        state = const EmployeeFormSuccess('Employee updated successfully');
      },
    );
  }

  Future<void> deleteEmployee(String id) async {
    state = const EmployeeFormLoading();
    final result = await _repository.deleteEmployee(id);
    result.fold(
      (failure) {
        AppLogger.error('Delete employee failed: ${failure.message}');
        state = EmployeeFormError(failure.message);
      },
      (_) {
        AppLogger.info('Delete employee success');
        state = const EmployeeFormSuccess('Employee deleted successfully');
      },
    );
  }
}