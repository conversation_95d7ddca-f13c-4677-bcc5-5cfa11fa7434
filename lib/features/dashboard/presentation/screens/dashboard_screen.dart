import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:pos_app/core/config/app_theme.dart';
import 'package:pos_app/features/auth/application/providers/auth_providers.dart';
import 'package:pos_app/features/auth/presentation/screens/login_screen.dart';
import 'package:pos_app/features/pos/presentation/screens/pos_screen.dart';
import 'package:pos_app/features/attendance/presentation/screens/attendance_screen.dart';
import 'package:pos_app/features/product/presentation/screens/product_list_screen.dart';
import 'package:pos_app/features/reports/presentation/screens/reports_screen.dart';
import 'package:pos_app/features/outlet/presentation/screens/outlet_list_screen.dart';
import 'package:pos_app/features/employee/presentation/screens/employee_list_screen.dart';

class DashboardScreen extends HookConsumerWidget {
  static const String routeName = '/dashboard';

  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final user = ref.watch(userProvider);
    final isAdmin = ref.watch(isAdminProvider);
    
    // Selected tab index
    final selectedIndex = useState(0);
    
    // Tab screens
    final screens = [
      const HomeTab(),
      const SalesTab(),
      const ProductsTab(),
      const MoreTab(),
    ];

    return Scaffold(
      body: IndexedStack(
        index: selectedIndex.value,
        children: screens,
      ),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: selectedIndex.value,
        onTap: (index) => selectedIndex.value = index,
        type: BottomNavigationBarType.fixed,
        backgroundColor: Theme.of(context).colorScheme.surface,
        selectedItemColor: AppTheme.primaryColor,
        unselectedItemColor: Colors.grey,
        showUnselectedLabels: true,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard_outlined),
            activeIcon: Icon(Icons.dashboard),
            label: 'Home',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.point_of_sale_outlined),
            activeIcon: Icon(Icons.point_of_sale),
            label: 'Sales',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.inventory_2_outlined),
            activeIcon: Icon(Icons.inventory_2),
            label: 'Products',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.more_horiz_outlined),
            activeIcon: Icon(Icons.more_horiz),
            label: 'More',
          ),
        ],
      ),
      floatingActionButton: selectedIndex.value == 1
          ? FloatingActionButton(
              onPressed: () {
                Navigator.pushNamed(context, POSScreen.routeName);
              },
              backgroundColor: AppTheme.primaryColor,
              child: const Icon(
                Icons.add_shopping_cart,
                color: Colors.white,
              ),
            )
          : null,
    );
  }
}

class HomeTab extends HookConsumerWidget {
  const HomeTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final user = ref.watch(userProvider);
    final isAdmin = ref.watch(isAdminProvider);

    return SafeArea(
      child: CustomScrollView(
        slivers: [
          SliverAppBar(
            title: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Hello, ${user?.name ?? 'User'}',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                Text(
                  isAdmin ? 'Admin' : 'Employee',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
            actions: [
              IconButton(
                icon: const Icon(Icons.logout),
                onPressed: () {
                  ref.read(authStateProvider.notifier).signOut();
                  Navigator.of(context).pushReplacementNamed(
                    LoginScreen.routeName,
                  );
                },
              ),
            ],
            floating: true,
            snap: true,
          ),
          SliverPadding(
            padding: const EdgeInsets.all(16),
            sliver: SliverToBoxAdapter(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Overview',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 16),
                  const DashboardStatsRow(),
                  const SizedBox(height: 24),
                  
                  Text(
                    'Quick Actions',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 16),
                  QuickActionsGrid(isAdmin: isAdmin),
                  const SizedBox(height: 24),
                  
                  Text(
                    'Recent Sales',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 16),
                  const RecentSalesList(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class DashboardStatsRow extends StatelessWidget {
  const DashboardStatsRow({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            context,
            'Today\'s Sales',
            'Rp 1,250,000',
            Icons.monetization_on,
            AppTheme.primaryColor,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildStatCard(
            context,
            'Total Items',
            '245',
            Icons.inventory_2,
            AppTheme.secondaryColor,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 24,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppTheme.textSecondaryDark,
                  ),
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
          ],
        ),
      ),
    );
  }
}

class QuickActionsGrid extends StatelessWidget {
  final bool isAdmin;

  const QuickActionsGrid({super.key, required this.isAdmin});

  @override
  Widget build(BuildContext context) {
    final actions = [
      _QuickAction(
        title: 'New Sale',
        icon: Icons.point_of_sale,
        color: AppTheme.primaryColor,
        onTap: () {
          Navigator.pushNamed(context, POSScreen.routeName);
        },
      ),
      _QuickAction(
        title: 'Products',
        icon: Icons.category,
        color: AppTheme.secondaryColor,
        onTap: () {
          Navigator.pushNamed(context, ProductListScreen.routeName);
        },
      ),
      _QuickAction(
        title: 'Clock In/Out',
        icon: Icons.timelapse,
        color: AppTheme.accentColor,
        onTap: () {
          Navigator.pushNamed(context, AttendanceScreen.routeName);
        },
      ),
      if (isAdmin)
        _QuickAction(
          title: 'Reports',
          icon: Icons.bar_chart,
          color: Colors.purple,
          onTap: () {
            Navigator.pushNamed(context, ReportsScreen.routeName);
          },
        ),
      if (isAdmin)
        _QuickAction(
          title: 'Outlets',
          icon: Icons.store,
          color: Colors.orange,
          onTap: () {
            Navigator.pushNamed(context, OutletListScreen.routeName);
          },
        ),
      if (isAdmin)
        _QuickAction(
          title: 'Employees',
          icon: Icons.people,
          color: Colors.green,
          onTap: () {
            Navigator.pushNamed(context, EmployeeListScreen.routeName);
          },
        ),
    ];

    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      mainAxisSpacing: 16,
      crossAxisSpacing: 16,
      children: actions.map((action) {
        return Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: InkWell(
            onTap: action.onTap,
            borderRadius: BorderRadius.circular(12),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: action.color.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      action.icon,
                      color: action.color,
                      size: 32,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    action.title,
                    style: Theme.of(context).textTheme.titleMedium,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        );
      }).toList(),
    );
  }
}

class _QuickAction {
  final String title;
  final IconData icon;
  final Color color;
  final VoidCallback onTap;

  const _QuickAction({
    required this.title,
    required this.icon,
    required this.color,
    required this.onTap,
  });
}

class RecentSalesList extends StatelessWidget {
  const RecentSalesList({super.key});

  @override
  Widget build(BuildContext context) {
    // TODO: Replace with actual data from sales repository
    final sales = [
      _SaleItem(
        id: 'S001',
        customerName: 'Walk-in Customer',
        total: 'Rp 120,000',
        date: 'Today, 10:30 AM',
        status: _SaleStatus.completed,
      ),
      _SaleItem(
        id: 'S002',
        customerName: 'Walk-in Customer',
        total: 'Rp 85,500',
        date: 'Today, 09:15 AM',
        status: _SaleStatus.completed,
      ),
      _SaleItem(
        id: 'S003',
        customerName: 'Walk-in Customer',
        total: 'Rp 254,000',
        date: 'Yesterday, 04:45 PM',
        status: _SaleStatus.pending,
      ),
    ];

    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: sales.length,
        separatorBuilder: (context, index) => const Divider(),
        itemBuilder: (context, index) {
          final sale = sales[index];
          return ListTile(
            title: Text(
              'Order #${sale.id}',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Text(sale.date),
            trailing: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  sale.total,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 4),
                _buildStatusBadge(sale.status),
              ],
            ),
            onTap: () {
              // TODO: Navigate to sale details
            },
          );
        },
      ),
    );
  }

  Widget _buildStatusBadge(_SaleStatus status) {
    Color color;
    String text;

    switch (status) {
      case _SaleStatus.completed:
        color = AppTheme.successColor;
        text = 'Completed';
        break;
      case _SaleStatus.pending:
        color = AppTheme.warningColor;
        text = 'Pending';
        break;
      case _SaleStatus.cancelled:
        color = AppTheme.errorColor;
        text = 'Cancelled';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}

class _SaleItem {
  final String id;
  final String customerName;
  final String total;
  final String date;
  final _SaleStatus status;

  const _SaleItem({
    required this.id,
    required this.customerName,
    required this.total,
    required this.date,
    required this.status,
  });
}

enum _SaleStatus {
  completed,
  pending,
  cancelled,
}

class SalesTab extends HookConsumerWidget {
  const SalesTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    'Sales History',
                    style: Theme.of(context).textTheme.headlineMedium,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: () {
                    Navigator.pushNamed(context, POSScreen.routeName);
                  },
                  icon: const Icon(Icons.add),
                  label: const Text('New Sale'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Expanded(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.point_of_sale_outlined,
                      size: 64,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No sales data available',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Start making sales to see them here',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[500],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class ProductsTab extends HookConsumerWidget {
  const ProductsTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isAdmin = ref.watch(isAdminProvider);

    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    'Products',
                    style: Theme.of(context).textTheme.headlineMedium,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: () {
                    Navigator.pushNamed(context, ProductListScreen.routeName);
                  },
                  icon: const Icon(Icons.list),
                  label: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Expanded(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.inventory_2_outlined,
                      size: 64,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Product Management',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Tap "View All" to manage products',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[500],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class MoreTab extends HookConsumerWidget {
  const MoreTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final user = ref.watch(userProvider);
    final isAdmin = ref.watch(isAdminProvider);

    return SafeArea(
      child: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // User profile card
          Card(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  CircleAvatar(
                    radius: 30,
                    backgroundColor: AppTheme.primaryColor,
                    child: Text(
                      user?.name.substring(0, 1) ?? 'U',
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          user?.name ?? 'User',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          user?.email ?? '<EMAIL>',
                          style: TextStyle(
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 4),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: AppTheme.primaryColor.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            isAdmin ? 'Admin' : 'Employee',
                            style: TextStyle(
                              color: AppTheme.primaryColor,
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 24),
          
          // Management section (Admin only)
          if (isAdmin) ...[
            Text(
              'Management',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Card(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                children: [
                  _buildMenuItem(
                    context,
                    'Outlet Management',
                    Icons.store_outlined,
                    () {
                      Navigator.pushNamed(context, OutletListScreen.routeName);
                    },
                  ),
                  const Divider(),
                  _buildMenuItem(
                    context,
                    'Employee Management',
                    Icons.people_outline,
                    () {
                      Navigator.pushNamed(context, EmployeeListScreen.routeName);
                    },
                  ),
                  const Divider(),
                  _buildMenuItem(
                    context,
                    'Reports & Analytics',
                    Icons.analytics_outlined,
                    () {
                      Navigator.pushNamed(context, ReportsScreen.routeName);
                    },
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
          ],
          
          // Features section
          Text(
            'Features',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Card(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              children: [
                _buildMenuItem(
                  context,
                  'Attendance',
                  Icons.access_time_outlined,
                  () {
                    Navigator.pushNamed(context, AttendanceScreen.routeName);
                  },
                ),
                const Divider(),
                _buildMenuItem(
                  context,
                  'Product Catalog',
                  Icons.inventory_2_outlined,
                  () {
                    Navigator.pushNamed(context, ProductListScreen.routeName);
                  },
                ),
                const Divider(),
                _buildMenuItem(
                  context,
                  'Point of Sale',
                  Icons.point_of_sale_outlined,
                  () {
                    Navigator.pushNamed(context, POSScreen.routeName);
                  },
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),
          
          // Support section
          Text(
            'Support',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Card(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              children: [
                _buildMenuItem(
                  context,
                  'Help & Support',
                  Icons.help_outline,
                  () {
                    // TODO: Navigate to help screen
                  },
                ),
                const Divider(),
                _buildMenuItem(
                  context,
                  'About',
                  Icons.info_outline,
                  () {
                    // TODO: Navigate to about screen
                  },
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),
          
          // Logout button
          ElevatedButton.icon(
            onPressed: () {
              ref.read(authStateProvider.notifier).signOut();
              Navigator.of(context).pushReplacementNamed(
                LoginScreen.routeName,
              );
            },
            icon: const Icon(Icons.logout),
            label: const Text('Logout'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMenuItem(
    BuildContext context,
    String title,
    IconData icon,
    VoidCallback onTap,
  ) {
    return ListTile(
      leading: Icon(
        icon,
        color: AppTheme.primaryColor,
      ),
      title: Text(title),
      trailing: const Icon(Icons.chevron_right),
      onTap: onTap,
    );
  }
}