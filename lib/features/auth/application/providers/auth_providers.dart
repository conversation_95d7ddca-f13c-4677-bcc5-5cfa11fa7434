import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:pos_app/core/utils/network_info.dart';
import 'package:pos_app/features/auth/application/notifiers/auth_notifier.dart';
import 'package:pos_app/features/auth/infrastructure/repositories/auth_repository.dart';
import 'package:pos_app/models/user.dart' as app_user;

// Infrastructure Providers
final networkInfoProvider = Provider<NetworkInfo>((ref) {
  return NetworkInfoImpl(InternetConnectionChecker.instance);
});

final authRepositoryProvider = Provider<AuthRepository>((ref) {
  return AuthRepositoryImpl(
    firebaseAuth: FirebaseAuth.instance,
    firestore: FirebaseFirestore.instance,
    networkInfo: ref.watch(networkInfoProvider),
  );
});

// Application Providers
final authStateProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  return AuthNotifier(ref.watch(authRepositoryProvider));
});

// User Provider
final userProvider = Provider<app_user.User?>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.maybeWhen(
    authenticated: (user) => user.user,
    orElse: () => null,
  );
});

// Role Providers
final isAdminProvider = Provider<bool>((ref) {
  final user = ref.watch(userProvider);
  return user?.role == app_user.UserRole.admin;
});

final isEmployeeProvider = Provider<bool>((ref) {
  final user = ref.watch(userProvider);
  return user?.role == app_user.UserRole.employee;
});