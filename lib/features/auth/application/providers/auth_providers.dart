import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:pos_app/core/utils/network_info.dart';
import 'package:pos_app/core/services/http_client_service.dart';
import 'package:pos_app/core/services/auth_service_http.dart';
import 'package:pos_app/features/auth/application/notifiers/auth_notifier.dart';
import 'package:pos_app/features/auth/infrastructure/repositories/auth_repository_http.dart';
import 'package:pos_app/models/user.dart' as app_user;

// Infrastructure Providers
final networkInfoProvider = Provider<NetworkInfo>((ref) {
  return NetworkInfoImpl(InternetConnectionChecker.instance);
});

// SharedPreferences provider (will be overridden in main.dart)
final sharedPreferencesProvider = Provider<SharedPreferences>((ref) {
  throw UnimplementedError('SharedPreferences must be overridden');
});

// HTTP Client provider
final httpClientServiceProvider = Provider<HttpClientService>((ref) {
  final prefs = ref.watch(sharedPreferencesProvider);
  return HttpClientService(prefs);
});

// Auth Service provider
final authServiceProvider = Provider<AuthService>((ref) {
  final httpClient = ref.watch(httpClientServiceProvider);
  return AuthServiceHttp(httpClient);
});

// Auth Repository provider (for backward compatibility)
final authRepositoryProvider = Provider<AuthRepository>((ref) {
  final authService = ref.watch(authServiceProvider);
  final networkInfo = ref.watch(networkInfoProvider);
  return AuthRepositoryHttp(
    authService: authService,
    networkInfo: networkInfo,
  );
});

// Application Providers
final authStateProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  return AuthNotifier(ref.watch(authRepositoryProvider));
});

// User Provider
final userProvider = Provider<app_user.User?>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.maybeWhen(
    authenticated: (user) => user.user,
    orElse: () => null,
  );
});

// Role Providers
final isAdminProvider = Provider<bool>((ref) {
  final user = ref.watch(userProvider);
  return user?.role == app_user.UserRole.admin;
});

final isEmployeeProvider = Provider<bool>((ref) {
  final user = ref.watch(userProvider);
  return user?.role == app_user.UserRole.employee;
});