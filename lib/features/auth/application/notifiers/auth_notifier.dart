import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:pos_app/core/utils/logger.dart';
import 'package:pos_app/features/auth/infrastructure/repositories/auth_repository.dart';
import 'package:pos_app/models/user.dart';

// Auth State
abstract class AuthState {
  const AuthState();

  T when<T>({
    required T Function(AuthInitial) initial,
    required T Function(AuthLoading) loading,
    required T Function(AuthAuthenticated) authenticated,
    required T Function(AuthUnauthenticated) unauthenticated,
    required T Function(AuthError) error,
  }) {
    if (this is AuthInitial) {
      return initial(this as AuthInitial);
    } else if (this is AuthLoading) {
      return loading(this as AuthLoading);
    } else if (this is AuthAuthenticated) {
      return authenticated(this as AuthAuthenticated);
    } else if (this is AuthUnauthenticated) {
      return unauthenticated(this as AuthUnauthenticated);
    } else if (this is AuthError) {
      return error(this as AuthError);
    }
    throw Exception('Unhandled state type: $this');
  }

  T maybeWhen<T>({
    T Function(AuthInitial)? initial,
    T Function(AuthLoading)? loading,
    T Function(AuthAuthenticated)? authenticated,
    T Function(AuthUnauthenticated)? unauthenticated,
    T Function(AuthError)? error,
    required T Function() orElse,
  }) {
    if (this is AuthInitial && initial != null) {
      return initial(this as AuthInitial);
    } else if (this is AuthLoading && loading != null) {
      return loading(this as AuthLoading);
    } else if (this is AuthAuthenticated && authenticated != null) {
      return authenticated(this as AuthAuthenticated);
    } else if (this is AuthUnauthenticated && unauthenticated != null) {
      return unauthenticated(this as AuthUnauthenticated);
    } else if (this is AuthError && error != null) {
      return error(this as AuthError);
    }
    return orElse();
  }
}

class AuthInitial extends AuthState {
  const AuthInitial();
}

class AuthLoading extends AuthState {
  const AuthLoading();
}

class AuthAuthenticated extends AuthState {
  final User user;
  const AuthAuthenticated(this.user);
}

class AuthUnauthenticated extends AuthState {
  const AuthUnauthenticated();
}

class AuthError extends AuthState {
  final String message;
  const AuthError(this.message);
}

// Auth Notifier
class AuthNotifier extends StateNotifier<AuthState> {
  final AuthRepository _authRepository;

  AuthNotifier(this._authRepository) : super(const AuthInitial()) {
    checkAuthStatus();
  }

  Future<void> checkAuthStatus() async {
    state = const AuthLoading();
    final result = await _authRepository.isSignedIn();
    
    result.fold(
      (failure) => state = AuthError(failure.message),
      (isSignedIn) async {
        if (isSignedIn) {
          final userResult = await _authRepository.getCurrentUser();
          userResult.fold(
            (failure) => state = const AuthUnauthenticated(),
            (user) => state = AuthAuthenticated(user),
          );
        } else {
          state = const AuthUnauthenticated();
        }
      },
    );
  }

  Future<void> signIn(String email, String password) async {
    state = const AuthLoading();
    
    final result = await _authRepository.signIn(email, password);
    
    result.fold(
      (failure) {
        AppLogger.error('Login failed: ${failure.message}');
        state = AuthError(failure.message);
      },
      (user) {
        AppLogger.info('Login successful: ${user.name}');
        state = AuthAuthenticated(user);
      },
    );
  }

  Future<void> signOut() async {
    state = const AuthLoading();
    
    final result = await _authRepository.signOut();
    
    result.fold(
      (failure) {
        AppLogger.error('Logout failed: ${failure.message}');
        state = AuthError(failure.message);
      },
      (_) {
        AppLogger.info('Logout successful');
        state = const AuthUnauthenticated();
      },
    );
  }
}