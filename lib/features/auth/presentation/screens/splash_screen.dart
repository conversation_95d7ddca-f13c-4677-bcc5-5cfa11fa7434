import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:pos_app/core/config/app_theme.dart';
import 'package:pos_app/features/auth/application/providers/auth_providers.dart';
import 'package:pos_app/features/auth/presentation/screens/login_screen.dart';
import 'package:pos_app/features/dashboard/presentation/screens/dashboard_screen.dart';

class SplashScreen extends HookConsumerWidget {
  static const String routeName = '/splash';

  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authStateProvider);

    useEffect(() {
      Future.delayed(const Duration(seconds: 2), () {
        authState.maybeWhen(
          authenticated: (_) => Navigator.of(context).pushReplacementNamed(
            DashboardScreen.routeName,
          ),
          unauthenticated: (_) => Navigator.of(context).pushReplacementNamed(
            LoginScreen.routeName,
          ),
          error: (message) => Navigator.of(context).pushReplacementNamed(
            LoginScreen.routeName,
          ),
          orElse: () {},
        );
      });
      return null;
    }, [authState]);

    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // App Logo
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: AppTheme.primaryColor,
                borderRadius: BorderRadius.circular(24),
              ),
              child: const Center(
                child: Icon(
                  Icons.point_of_sale,
                  size: 64,
                  color: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 24),
            
            // App Name
            Text(
              'POS System',
              style: Theme.of(context).textTheme.displaySmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
            ),
            const SizedBox(height: 8),
            
            // Tagline
            Text(
              'Manage your outlets efficiently',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: AppTheme.textSecondaryDark,
                  ),
            ),
            const SizedBox(height: 48),
            
            // Loading Indicator
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
            ),
          ],
        ),
      ),
    );
  }
}