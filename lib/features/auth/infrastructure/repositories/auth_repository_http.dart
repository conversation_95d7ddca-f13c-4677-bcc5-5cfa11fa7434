import 'package:dartz/dartz.dart';
import 'package:pos_app/core/constants/app_constants.dart';
import 'package:pos_app/core/errors/failures.dart';
import 'package:pos_app/core/services/auth_service_http.dart';
import 'package:pos_app/core/utils/logger.dart';
import 'package:pos_app/core/utils/network_info.dart';
import 'package:pos_app/models/user.dart' as app_user;
import 'auth_repository.dart';

class AuthRepositoryHttp implements AuthRepository {
  final AuthService _authService;
  final NetworkInfo _networkInfo;

  AuthRepositoryHttp({
    required AuthService authService,
    required NetworkInfo networkInfo,
  })  : _authService = authService,
        _networkInfo = networkInfo;

  @override
  Future<Either<Failure, app_user.User>> signIn(
    String email,
    String password,
  ) async {
    try {
      final isConnected = await _networkInfo.isConnected;
      if (!isConnected) {
        return const Left(NetworkFailure(AppConstants.networkErrorMessage));
      }

      final result = await _authService.signIn(email, password);
      return result;
    } catch (e) {
      AppLogger.error('Auth repository sign in error', e);
      return Left(UnknownFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> signOut() async {
    try {
      final result = await _authService.signOut();
      return result;
    } catch (e) {
      AppLogger.error('Auth repository sign out error', e);
      return Left(UnknownFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> isSignedIn() async {
    try {
      final result = await _authService.isSignedIn();
      return result;
    } catch (e) {
      AppLogger.error('Auth repository check sign in error', e);
      return const Right(false);
    }
  }

  @override
  Future<Either<Failure, app_user.User>> getCurrentUser() async {
    try {
      final result = await _authService.getCurrentUser();
      return result;
    } catch (e) {
      AppLogger.error('Auth repository get current user error', e);
      return Left(UnknownFailure(e.toString()));
    }
  }
}
