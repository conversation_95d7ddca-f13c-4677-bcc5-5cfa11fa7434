import 'package:dartz/dartz.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:pos_app/core/constants/app_constants.dart';
import 'package:pos_app/core/errors/failures.dart';
import 'package:pos_app/core/utils/logger.dart';
import 'package:pos_app/core/utils/network_info.dart';
import 'package:pos_app/models/user.dart' as app_user;

abstract class AuthRepository {
  Future<Either<Failure, app_user.User>> signIn(String email, String password);
  Future<Either<Failure, void>> signOut();
  Future<Either<Failure, bool>> isSignedIn();
  Future<Either<Failure, app_user.User>> getCurrentUser();
}

class AuthRepositoryImpl implements AuthRepository {
  final firebase_auth.FirebaseAuth _firebaseAuth;
  final FirebaseFirestore _firestore;
  final NetworkInfo _networkInfo;

  AuthRepositoryImpl({
    required firebase_auth.FirebaseAuth firebaseAuth,
    required FirebaseFirestore firestore,
    required NetworkInfo networkInfo,
  })  : _firebaseAuth = firebaseAuth,
        _firestore = firestore,
        _networkInfo = networkInfo;

  @override
  Future<Either<Failure, app_user.User>> signIn(
    String email,
    String password,
  ) async {
    try {
      final isConnected = await _networkInfo.isConnected;
      if (!isConnected) {
        return const Left(NetworkFailure(AppConstants.networkErrorMessage));
      }

      final userCredential = await _firebaseAuth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (userCredential.user == null) {
        return const Left(
          AuthFailure('Authentication failed. Please try again.'),
        );
      }

      final userDoc = await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userCredential.user!.uid)
          .get();

      if (!userDoc.exists) {
        await _firebaseAuth.signOut();
        return const Left(
          AuthFailure('User data not found. Please contact admin.'),
        );
      }

      final userData = userDoc.data()!;
      if (!(userData['isActive'] as bool? ?? true)) {
        await _firebaseAuth.signOut();
        return const Left(
          AuthFailure('Account has been deactivated. Please contact admin.'),
        );
      }

      final user = app_user.User.fromMap(userData);
      return Right(user);
    } on firebase_auth.FirebaseAuthException catch (e) {
      AppLogger.error('Login error', e);
      switch (e.code) {
        case 'user-not-found':
        case 'wrong-password':
          return const Left(
            AuthFailure('Invalid email or password. Please try again.'),
          );
        case 'user-disabled':
          return const Left(
            AuthFailure('Account has been disabled. Please contact admin.'),
          );
        case 'too-many-requests':
          return const Left(
            AuthFailure('Too many unsuccessful login attempts. Try again later.'),
          );
        default:
          return Left(AuthFailure(e.message ?? AppConstants.unknownErrorMessage));
      }
    } catch (e) {
      AppLogger.error('Login unexpected error', e);
      return Left(UnknownFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> signOut() async {
    try {
      await _firebaseAuth.signOut();
      return const Right(null);
    } catch (e) {
      AppLogger.error('Logout error', e);
      return Left(UnknownFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> isSignedIn() async {
    try {
      final currentUser = _firebaseAuth.currentUser;
      return Right(currentUser != null);
    } catch (e) {
      AppLogger.error('Check auth status error', e);
      return Left(UnknownFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, app_user.User>> getCurrentUser() async {
    try {
      final currentUser = _firebaseAuth.currentUser;
      if (currentUser == null) {
        return const Left(AuthFailure('User not authenticated'));
      }

      final userDoc = await _firestore
          .collection(AppConstants.usersCollection)
          .doc(currentUser.uid)
          .get();

      if (!userDoc.exists) {
        return const Left(AuthFailure('User data not found'));
      }

      final userData = userDoc.data()!;
      final user = app_user.User.fromMap(userData);
      return Right(user);
    } catch (e) {
      AppLogger.error('Get current user error', e);
      return Left(UnknownFailure(e.toString()));
    }
  }
}