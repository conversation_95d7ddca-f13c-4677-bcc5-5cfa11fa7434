import 'package:dartz/dartz.dart';
import 'package:pos_app/core/errors/failures.dart';
import 'package:pos_app/models/sale.dart';
import 'package:pos_app/repositories/pos_repository_http.dart' as http_repo;
import 'pos_repository.dart';

/// Adapter to make HTTP repository compatible with existing interface
class POSRepositoryAdapter implements POSRepository {
  final http_repo.POSRepository _httpRepository;

  POSRepositoryAdapter(this._httpRepository);

  @override
  Future<Either<Failure, Sale>> createSale(Sale sale) async {
    return await _httpRepository.createSale(sale);
  }

  @override
  Future<Either<Failure, List<Sale>>> getSales({
    String? outletId,
    DateTime? startDate,
    DateTime? endDate,
    int page = 1,
    int limit = 20,
  }) async {
    return await _httpRepository.getSales(
      outletId: outletId,
      startDate: startDate,
      endDate: endDate,
      page: page,
      limit: limit,
    );
  }

  @override
  Future<Either<Failure, void>> syncOfflineSales() async {
    return await _httpRepository.syncOfflineSales();
  }

  @override
  Future<int> getOfflineSalesCount() async {
    return await _httpRepository.getOfflineSalesCount();
  }
}
