import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dartz/dartz.dart';
import 'package:hive/hive.dart';
import 'package:pos_app/core/constants/app_constants.dart';
import 'package:pos_app/core/errors/failures.dart';
import 'package:pos_app/core/utils/logger.dart';
import 'package:pos_app/core/utils/network_info.dart';
import 'package:pos_app/models/sale.dart';

abstract class POSRepository {
  Future<Either<Failure, Sale>> createSale(Sale sale);
  Future<Either<Failure, void>> syncOfflineSales();
}

class POSRepositoryImpl implements POSRepository {
  final FirebaseFirestore _firestore;
  final NetworkInfo _networkInfo;
  final Box _saleBox;

  POSRepositoryImpl({
    required FirebaseFirestore firestore,
    required NetworkInfo networkInfo,
    required Box saleBox,
  })  : _firestore = firestore,
        _networkInfo = networkInfo,
        _saleBox = saleBox;

  @override
  Future<Either<Failure, Sale>> createSale(Sale sale) async {
    try {
      final isConnected = await _networkInfo.isConnected;

      if (!isConnected) {
        // Save offline
        await _saleBox.add(sale.toMap());
        return Right(sale);
      }

      // Save online
      final docRef = _firestore.collection(AppConstants.salesCollection).doc();
      final newSale = sale.copyWith(
        id: docRef.id,
        isSynced: true,
      );

      // Start transaction
      await _firestore.runTransaction((transaction) async {
        // Create sale
        transaction.set(docRef, newSale.toMap());

        // Update product stock
        for (final item in sale.items) {
          final productRef = _firestore
              .collection(AppConstants.productsCollection)
              .doc(item.productId);

          final productDoc = await transaction.get(productRef);
          if (!productDoc.exists) {
            throw Exception('Product not found: ${item.productId}');
          }

          final currentStock =
              (productDoc.data()?['stock'] as Map<String, dynamic>)[sale.outletId] ?? 0;
          final newStock = currentStock - item.quantity;

          if (newStock < 0) {
            throw Exception(
              'Insufficient stock for product: ${item.productName}',
            );
          }

          transaction.update(productRef, {
            'stock.${sale.outletId}': newStock,
            'updatedAt': FieldValue.serverTimestamp(),
          });
        }
      });

      return Right(newSale);
    } catch (e) {
      AppLogger.error('Create sale error', e);
      return Left(UnknownFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> syncOfflineSales() async {
    try {
      final isConnected = await _networkInfo.isConnected;
      if (!isConnected) {
        return const Left(NetworkFailure(AppConstants.networkErrorMessage));
      }

      final offlineSales = _saleBox.values
          .map((data) => Sale.fromMap(Map<String, dynamic>.from(data)))
          .where((sale) => !sale.isSynced)
          .toList();

      for (final sale in offlineSales) {
        final result = await createSale(sale);
        if (result.isRight()) {
          await _saleBox.delete(sale.id);
        }
      }

      return const Right(null);
    } catch (e) {
      AppLogger.error('Sync offline sales error', e);
      return Left(UnknownFailure(e.toString()));
    }
  }
}