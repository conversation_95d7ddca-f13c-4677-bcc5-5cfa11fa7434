import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:pos_app/core/config/app_theme.dart';
import 'package:pos_app/core/widgets/app_button.dart';
import 'package:pos_app/core/widgets/app_error.dart';
import 'package:pos_app/core/widgets/app_loading.dart';
import 'package:pos_app/features/auth/application/providers/auth_providers.dart';
import 'package:pos_app/features/pos/application/notifiers/pos_notifier.dart';
import 'package:pos_app/features/pos/application/providers/pos_providers.dart';
import 'package:pos_app/features/product/application/notifiers/product_notifier.dart';
import 'package:pos_app/features/product/application/providers/product_providers.dart';
import 'package:pos_app/models/product.dart';
import 'package:intl/intl.dart';

class POSScreen extends HookConsumerWidget {
  static const String routeName = '/pos';

  const POSScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final user = ref.watch(userProvider);
    final productState = ref.watch(productStateProvider);
    final cartState = ref.watch(cartProvider);
    final posState = ref.watch(posProvider);

    // Load products on init
    useEffect(() {
      if (user != null) {
        if (user.outletId != null) {
          ref.read(productStateProvider.notifier).getProductsByOutlet(
                user.outletId!,
              );
        } else {
          ref.read(productStateProvider.notifier).getProducts();
        }
      }
      return null;
    }, [user]);

    // Handle POS state changes
    ref.listen(posProvider, (previous, next) {
      if (next is POSSuccess) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(next.message),
            backgroundColor: AppTheme.successColor,
          ),
        );
        // Show success dialog
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Sale Completed'),
            content: const Text('Transaction has been processed successfully!'),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  Navigator.pop(context); // Go back to dashboard
                },
                child: const Text('OK'),
              ),
            ],
          ),
        );
      } else if (next is POSError) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(next.message),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    });

    return Scaffold(
      appBar: AppBar(
        title: const Text('Point of Sale'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: LayoutBuilder(
        builder: (context, constraints) {
          if (constraints.maxWidth > 800) {
            // Desktop/Tablet layout
            return Row(
              children: [
                // Product List
                Expanded(
                  flex: 2,
                  child: _buildProductSection(context, ref, productState),
                ),
                // Cart
                Expanded(
                  flex: 1,
                  child: _buildCartSection(context, ref, cartState, posState, user),
                ),
              ],
            );
          } else {
            // Mobile layout
            return Column(
              children: [
                // Cart Summary (collapsed)
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Theme.of(context).cardColor,
                    border: Border(
                      bottom: BorderSide(
                        color: Colors.grey.withOpacity(0.2),
                      ),
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(
                          '${cartState.items.length} items - Rp ${NumberFormat('#,###').format(cartState.grandTotal)}',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                      ),
                      ElevatedButton(
                        onPressed: cartState.items.isNotEmpty
                            ? () {
                                _showCartBottomSheet(context, ref, cartState, posState, user);
                              }
                            : null,
                        child: const Text('View Cart'),
                      ),
                    ],
                  ),
                ),
                // Product List
                Expanded(
                  child: _buildProductSection(context, ref, productState),
                ),
              ],
            );
          }
        },
      ),
    );
  }

  Widget _buildProductSection(BuildContext context, WidgetRef ref, ProductState productState) {
    if (productState is ProductLoading) {
      return const AppLoading();
    }

    if (productState is ProductError) {
      return AppError(
        message: productState.message,
        onRetry: () {
          final user = ref.read(userProvider);
          if (user != null) {
            if (user.outletId != null) {
              ref.read(productStateProvider.notifier).getProductsByOutlet(
                    user.outletId!,
                  );
            } else {
              ref.read(productStateProvider.notifier).getProducts();
            }
          }
        },
      );
    }

    if (productState is ProductSuccess) {
      return _ProductGrid(products: productState.products);
    }

    return const SizedBox.shrink();
  }

  Widget _buildCartSection(BuildContext context, WidgetRef ref, CartState cartState, POSState posState, user) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        border: Border(
          left: BorderSide(
            color: Colors.grey.withOpacity(0.2),
          ),
        ),
      ),
      child: Column(
        children: [
          // Cart Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: Colors.grey.withOpacity(0.2),
                ),
              ),
            ),
            child: Row(
              children: [
                const Icon(Icons.shopping_cart),
                const SizedBox(width: 8),
                Text(
                  'Cart (${cartState.items.length})',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),
                const Spacer(),
                if (cartState.items.isNotEmpty)
                  TextButton(
                    onPressed: () {
                      ref.read(cartProvider.notifier).clear();
                    },
                    child: const Text('Clear'),
                  ),
              ],
            ),
          ),

          // Cart Items
          Expanded(
            child: cartState.items.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.shopping_cart_outlined,
                          size: 64,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Cart is empty',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Add products to start selling',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[500],
                          ),
                        ),
                      ],
                    ),
                  )
                : ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: cartState.items.length,
                    itemBuilder: (context, index) {
                      final item = cartState.items[index];
                      return Card(
                        margin: const EdgeInsets.only(bottom: 8),
                        child: Padding(
                          padding: const EdgeInsets.all(12),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Expanded(
                                    child: Text(
                                      item.productName,
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                  IconButton(
                                    icon: const Icon(Icons.delete, color: Colors.red),
                                    onPressed: () {
                                      ref.read(cartProvider.notifier).removeItem(
                                            item.productId,
                                          );
                                    },
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Row(
                                children: [
                                  Text(
                                    'Rp ${NumberFormat('#,###').format(item.price)}',
                                    style: TextStyle(
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                  const Spacer(),
                                  Row(
                                    children: [
                                      IconButton(
                                        onPressed: item.quantity > 1
                                            ? () {
                                                ref.read(cartProvider.notifier).updateQuantity(
                                                      item.productId,
                                                      item.quantity - 1,
                                                    );
                                              }
                                            : null,
                                        icon: const Icon(Icons.remove),
                                        iconSize: 20,
                                      ),
                                      Container(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 12,
                                          vertical: 4,
                                        ),
                                        decoration: BoxDecoration(
                                          border: Border.all(color: Colors.grey),
                                          borderRadius: BorderRadius.circular(4),
                                        ),
                                        child: Text(
                                          '${item.quantity}',
                                          style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                      IconButton(
                                        onPressed: () {
                                          ref.read(cartProvider.notifier).updateQuantity(
                                                item.productId,
                                                item.quantity + 1,
                                              );
                                        },
                                        icon: const Icon(Icons.add),
                                        iconSize: 20,
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                              const SizedBox(height: 4),
                              Text(
                                'Total: Rp ${NumberFormat('#,###').format(item.total)}',
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
          ),

          // Cart Summary
          if (cartState.items.isNotEmpty)
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).scaffoldBackgroundColor,
                border: Border(
                  top: BorderSide(
                    color: Colors.grey.withOpacity(0.2),
                  ),
                ),
              ),
              child: Column(
                children: [
                  // Subtotal
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('Subtotal'),
                      Text(
                        'Rp ${NumberFormat('#,###').format(cartState.subtotal)}',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),

                  // Tax
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('Tax (11%)'),
                      Text(
                        'Rp ${NumberFormat('#,###').format(cartState.tax)}',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),

                  // Discount
                  Row(
                    children: [
                      const Text('Discount'),
                      const SizedBox(width: 8),
                      Expanded(
                        child: TextFormField(
                          initialValue: cartState.discount.toString(),
                          keyboardType: TextInputType.number,
                          decoration: const InputDecoration(
                            prefixText: 'Rp ',
                            contentPadding: EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 0,
                            ),
                          ),
                          onChanged: (value) {
                            final discount = double.tryParse(value) ?? 0;
                            ref.read(cartProvider.notifier).setDiscount(
                                  discount,
                                );
                          },
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Grand Total
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Grand Total',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                        ),
                      ),
                      Text(
                        'Rp ${NumberFormat('#,###').format(cartState.grandTotal)}',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                          color: AppTheme.primaryColor,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Process Button
                  AppButton(
                    label: 'Process Payment',
                    onPressed: () {
                      if (user != null) {
                        ref.read(posProvider.notifier).processSale(
                              user.outletId ?? 'default',
                              user.id,
                            );
                      }
                    },
                    isLoading: posState is POSLoading,
                    width: double.infinity,
                    icon: Icons.payment,
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  void _showCartBottomSheet(BuildContext context, WidgetRef ref, CartState cartState, POSState posState, user) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        child: _buildCartSection(context, ref, cartState, posState, user),
      ),
    );
  }
}

class _ProductGrid extends HookConsumerWidget {
  final List<Product> products;

  const _ProductGrid({required this.products});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final searchController = useTextEditingController();
    final filteredProducts = useState(products);

    // Filter products on search
    useEffect(() {
      void filterProducts() {
        final query = searchController.text.toLowerCase();
        filteredProducts.value = products.where((product) {
          return product.name.toLowerCase().contains(query) ||
              product.category.toLowerCase().contains(query);
        }).toList();
      }

      searchController.addListener(filterProducts);
      return () => searchController.removeListener(filterProducts);
    }, [searchController, products]);

    return Column(
      children: [
        // Search Bar
        Padding(
          padding: const EdgeInsets.all(16),
          child: TextFormField(
            controller: searchController,
            decoration: const InputDecoration(
              hintText: 'Search products...',
              prefixIcon: Icon(Icons.search),
            ),
          ),
        ),

        // Product Grid
        Expanded(
          child: filteredProducts.value.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.search_off,
                        size: 64,
                        color: Colors.grey[400],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'No products found',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                )
              : GridView.builder(
                  padding: const EdgeInsets.all(16),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: MediaQuery.of(context).size.width > 800 ? 4 : 2,
                    childAspectRatio: 0.8,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                  ),
                  itemCount: filteredProducts.value.length,
                  itemBuilder: (context, index) {
                    final product = filteredProducts.value[index];
                    return _ProductCard(product: product);
                  },
                ),
        ),
      ],
    );
  }
}

class _ProductCard extends HookConsumerWidget {
  final Product product;

  const _ProductCard({required this.product});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final user = ref.watch(userProvider);
    final stock = product.getStockForOutlet(user?.outletId ?? '');

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: stock > 0
            ? () {
                _showAddToCartDialog(context, ref, product);
              }
            : null,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Product Image/Icon
              Expanded(
                child: Center(
                  child: Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: stock > 0
                          ? AppTheme.primaryColor.withOpacity(0.1)
                          : Colors.grey.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.inventory_2_outlined,
                      size: 48,
                      color: stock > 0 ? AppTheme.primaryColor : Colors.grey,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 12),

              // Product Info
              Text(
                product.name,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                  color: stock > 0 ? null : Colors.grey,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              Text(
                'Rp ${NumberFormat('#,###').format(product.price)}',
                style: TextStyle(
                  color: stock > 0 ? AppTheme.primaryColor : Colors.grey,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  Text(
                    'Stock: $stock',
                    style: TextStyle(
                      color: stock > 0 ? Colors.green : Colors.red,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (stock == 0) ...[
                    const Spacer(),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.red.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: const Text(
                        'Out of Stock',
                        style: TextStyle(
                          color: Colors.red,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showAddToCartDialog(BuildContext context, WidgetRef ref, Product product) {
    final quantityController = TextEditingController(text: '1');
    final user = ref.read(userProvider);
    final maxStock = product.getStockForOutlet(user?.outletId ?? '');

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(product.name),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Rp ${NumberFormat('#,###').format(product.price)}',
              style: const TextStyle(
                color: AppTheme.primaryColor,
                fontWeight: FontWeight.bold,
                fontSize: 18,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              product.description,
              style: TextStyle(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Available Stock: $maxStock',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: quantityController,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'Quantity',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                final quantity = int.tryParse(value ?? '');
                if (quantity == null || quantity <= 0) {
                  return 'Please enter a valid quantity';
                }
                if (quantity > maxStock) {
                  return 'Quantity exceeds available stock';
                }
                return null;
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              final quantity = int.tryParse(quantityController.text) ?? 1;
              if (quantity > 0 && quantity <= maxStock) {
                ref.read(cartProvider.notifier).addItem(
                      CartItem(
                        productId: product.id,
                        productName: product.name,
                        price: product.price,
                        quantity: quantity,
                      ),
                    );
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('${product.name} added to cart'),
                    backgroundColor: AppTheme.successColor,
                  ),
                );
              }
            },
            child: const Text('Add to Cart'),
          ),
        ],
      ),
    );
  }
}