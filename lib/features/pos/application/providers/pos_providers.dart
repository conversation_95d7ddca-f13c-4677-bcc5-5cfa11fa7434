import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:pos_app/features/auth/application/providers/auth_providers.dart';
import 'package:pos_app/features/pos/application/notifiers/pos_notifier.dart';
import 'package:pos_app/features/pos/infrastructure/repositories/pos_repository.dart';

// Repository Provider
final posRepositoryProvider = Provider<POSRepository>((ref) {
  return POSRepositoryImpl(
    firestore: FirebaseFirestore.instance,
    networkInfo: ref.watch(networkInfoProvider),
    saleBox: Hive.box('saleBox'),
  );
});

// Cart Provider
final cartProvider = StateNotifierProvider<CartNotifier, CartState>((ref) {
  return CartNotifier();
});

// POS Provider
final posProvider = StateNotifierProvider<POSNotifier, POSState>((ref) {
  return POSNotifier(
    ref.watch(posRepositoryProvider),
    ref.watch(cartProvider.notifier),
  );
});