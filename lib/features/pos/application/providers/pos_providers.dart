import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:pos_app/features/auth/application/providers/auth_providers.dart';
import 'package:pos_app/features/pos/application/notifiers/pos_notifier.dart';
import 'package:pos_app/features/pos/infrastructure/repositories/pos_repository.dart';
import 'package:pos_app/features/pos/infrastructure/repositories/pos_repository_adapter.dart';
import 'package:pos_app/repositories/pos_repository_http.dart' as http_repo;
import 'package:pos_app/core/constants/app_constants.dart';

// Repository Provider
final posRepositoryProvider = Provider<POSRepository>((ref) {
  final httpClient = ref.watch(httpClientServiceProvider);
  final networkInfo = ref.watch(networkInfoProvider);
  final saleBox = Hive.box(AppConstants.saleBox);

  final httpRepository = http_repo.POSRepositoryHttp(
    httpClient: httpClient,
    networkInfo: networkInfo,
    saleBox: saleBox,
  );

  return POSRepositoryAdapter(httpRepository);
});

// Cart Provider
final cartProvider = StateNotifierProvider<CartNotifier, CartState>((ref) {
  return CartNotifier();
});

// POS Provider
final posProvider = StateNotifierProvider<POSNotifier, POSState>((ref) {
  return POSNotifier(
    ref.watch(posRepositoryProvider),
    ref.watch(cartProvider.notifier),
  );
});