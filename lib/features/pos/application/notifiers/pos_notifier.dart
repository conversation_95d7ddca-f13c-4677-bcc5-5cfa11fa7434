import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:pos_app/core/utils/logger.dart';
import 'package:pos_app/features/pos/infrastructure/repositories/pos_repository.dart';
import 'package:pos_app/models/sale.dart';

// Cart Item State
class CartItem {
  final String productId;
  final String productName;
  final double price;
  final int quantity;
  final double total;

  CartItem({
    required this.productId,
    required this.productName,
    required this.price,
    required this.quantity,
  }) : total = price * quantity;
}

// POS State
abstract class POSState {
  const POSState();
}

class POSInitial extends POSState {
  const POSInitial();
}

class POSLoading extends POSState {
  const POSLoading();
}

class POSSuccess extends POSState {
  final String message;
  const POSSuccess(this.message);
}

class POSError extends POSState {
  final String message;
  const POSError(this.message);
}

// Cart State
class CartState {
  final List<CartItem> items;
  final double subtotal;
  final double tax;
  final double discount;
  final double grandTotal;
  final bool isLoading;

  CartState({
    required this.items,
    required this.subtotal,
    required this.tax,
    required this.discount,
    required this.grandTotal,
    this.isLoading = false,
  });

  factory CartState.initial() => CartState(
        items: [],
        subtotal: 0,
        tax: 0,
        discount: 0,
        grandTotal: 0,
      );

  CartState copyWith({
    List<CartItem>? items,
    double? subtotal,
    double? tax,
    double? discount,
    double? grandTotal,
    bool? isLoading,
  }) {
    return CartState(
      items: items ?? this.items,
      subtotal: subtotal ?? this.subtotal,
      tax: tax ?? this.tax,
      discount: discount ?? this.discount,
      grandTotal: grandTotal ?? this.grandTotal,
      isLoading: isLoading ?? this.isLoading,
    );
  }
}

// Cart Notifier
class CartNotifier extends StateNotifier<CartState> {
  CartNotifier() : super(CartState.initial());

  void addItem(CartItem item) {
    final existingIndex = state.items.indexWhere(
      (i) => i.productId == item.productId,
    );

    final List<CartItem> updatedItems;
    if (existingIndex >= 0) {
      updatedItems = [...state.items];
      updatedItems[existingIndex] = CartItem(
        productId: item.productId,
        productName: item.productName,
        price: item.price,
        quantity: state.items[existingIndex].quantity + item.quantity,
      );
    } else {
      updatedItems = [...state.items, item];
    }

    _updateTotals(updatedItems);
  }

  void removeItem(String productId) {
    final updatedItems = state.items
        .where((item) => item.productId != productId)
        .toList();
    _updateTotals(updatedItems);
  }

  void updateQuantity(String productId, int quantity) {
    if (quantity <= 0) {
      removeItem(productId);
      return;
    }

    final updatedItems = state.items.map((item) {
      if (item.productId == productId) {
        return CartItem(
          productId: item.productId,
          productName: item.productName,
          price: item.price,
          quantity: quantity,
        );
      }
      return item;
    }).toList();

    _updateTotals(updatedItems);
  }

  void setDiscount(double discount) {
    _updateTotals(state.items, discount: discount);
  }

  void clear() {
    state = CartState.initial();
  }

  void _updateTotals(List<CartItem> items, {double? discount}) {
    final subtotal = items.fold<double>(
      0,
      (sum, item) => sum + item.total,
    );

    final tax = subtotal * 0.11; // 11% tax
    final finalDiscount = discount ?? state.discount;
    final grandTotal = subtotal + tax - finalDiscount;

    state = state.copyWith(
      items: items,
      subtotal: subtotal,
      tax: tax,
      discount: finalDiscount,
      grandTotal: grandTotal,
    );
  }
}

// POS Notifier
class POSNotifier extends StateNotifier<POSState> {
  final POSRepository _repository;
  final CartNotifier _cartNotifier;

  POSNotifier(this._repository, this._cartNotifier)
      : super(const POSInitial());

  Future<void> processSale(String outletId, String employeeId) async {
    if (_cartNotifier.state.items.isEmpty) {
      state = const POSError('Cart is empty');
      return;
    }

    state = const POSLoading();

    final sale = Sale(
      id: '',
      outletId: outletId,
      employeeId: employeeId,
      items: _cartNotifier.state.items
          .map((item) => SaleItem(
                productId: item.productId,
                productName: item.productName,
                price: item.price,
                quantity: item.quantity,
                total: item.total,
              ))
          .toList(),
      subtotal: _cartNotifier.state.subtotal,
      tax: _cartNotifier.state.tax,
      discount: _cartNotifier.state.discount,
      grandTotal: _cartNotifier.state.grandTotal,
      paymentMethod: PaymentMethod.cash,
      saleDate: DateTime.now(),
      createdAt: DateTime.now(),
    );

    final result = await _repository.createSale(sale);

    result.fold(
      (failure) {
        AppLogger.error('Process sale failed: ${failure.message}');
        state = POSError(failure.message);
      },
      (sale) {
        AppLogger.info('Process sale success: ${sale.id}');
        _cartNotifier.clear();
        state = const POSSuccess('Sale processed successfully');
      },
    );
  }
}