import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:pos_app/core/config/app_theme.dart';
import 'package:pos_app/core/constants/app_constants.dart';
import 'package:pos_app/core/widgets/app_button.dart';
import 'package:pos_app/features/outlet/application/providers/outlet_providers.dart';
import 'package:pos_app/features/product/application/notifiers/product_notifier.dart';
import 'package:pos_app/features/product/application/providers/product_providers.dart';
import 'package:pos_app/models/product.dart';

class ProductFormScreen extends HookConsumerWidget {
  static const String routeName = '/product-form';

  final Product? product;

  const ProductFormScreen({
    super.key,
    this.product,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = useMemoized(() => GlobalKey<FormState>());
    final nameController = useTextEditingController(text: product?.name);
    final descriptionController = useTextEditingController(text: product?.description);
    final priceController = useTextEditingController(
      text: product?.price.toString() ?? '0',
    );
    final categoryController = useTextEditingController(text: product?.category);
    final stock = useState<Map<String, int>>(product?.stock ?? {});

    // Load outlets
    useEffect(() {
      ref.read(outletStateProvider.notifier).getOutlets();
      return null;
    }, const []);

    // Watch form state
    final formState = ref.watch(productFormStateProvider);
    final outletState = ref.watch(outletStateProvider);

    // Handle form state changes
    ref.listen(productFormStateProvider, (previous, next) {
      if (next is ProductFormSuccess) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(next.message),
            backgroundColor: AppTheme.successColor,
          ),
        );
        Navigator.pop(context);
      } else if (next is ProductFormError) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(next.message),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    });

    // Handle form submission
    Future<void> handleSubmit() async {
      if (formKey.currentState?.validate() ?? false) {
        final newProduct = (product ?? Product.empty()).copyWith(
          name: nameController.text,
          description: descriptionController.text,
          price: double.parse(priceController.text),
          category: categoryController.text,
          stock: stock.value,
        );

        if (product == null) {
          await ref.read(productFormStateProvider.notifier).createProduct(
                newProduct,
              );
        } else {
          await ref.read(productFormStateProvider.notifier).updateProduct(
                newProduct,
              );
        }
      }
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(product == null ? 'Add Product' : 'Edit Product'),
      ),
      body: Form(
        key: formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            TextFormField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'Product Name',
                hintText: 'Enter product name',
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return AppConstants.requiredFieldMessage;
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description',
                hintText: 'Enter product description',
              ),
              maxLines: 3,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return AppConstants.requiredFieldMessage;
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: priceController,
              decoration: const InputDecoration(
                labelText: 'Price',
                hintText: 'Enter product price',
                prefixText: 'Rp ',
              ),
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return AppConstants.requiredFieldMessage;
                }
                if (double.tryParse(value) == null) {
                  return 'Invalid price';
                }
                if (double.parse(value) <= 0) {
                  return 'Price must be greater than 0';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: categoryController,
              decoration: const InputDecoration(
                labelText: 'Category',
                hintText: 'Enter product category',
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return AppConstants.requiredFieldMessage;
                }
                return null;
              },
            ),
            const SizedBox(height: 24),
            Text(
              'Stock',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            outletState.maybeWhen(
              success: (outlets) => Column(
                children: outlets.outlets.map((outlet) {
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 16),
                    child: TextFormField(
                      initialValue: stock.value[outlet.id]?.toString() ?? '0',
                      decoration: InputDecoration(
                        labelText: outlet.name,
                        hintText: 'Enter stock quantity',
                      ),
                      keyboardType: TextInputType.number,
                      onChanged: (value) {
                        final quantity = int.tryParse(value) ?? 0;
                        stock.value = {
                          ...stock.value,
                          outlet.id: quantity,
                        };
                      },
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return AppConstants.requiredFieldMessage;
                        }
                        if (int.tryParse(value) == null) {
                          return 'Invalid quantity';
                        }
                        if (int.parse(value) < 0) {
                          return 'Quantity cannot be negative';
                        }
                        return null;
                      },
                    ),
                  );
                }).toList(),
              ),
              orElse: () => const SizedBox.shrink(),
            ),
            const SizedBox(height: 24),
            AppButton(
              label: product == null ? 'Create Product' : 'Update Product',
              onPressed: handleSubmit,
              isLoading: formState is ProductFormLoading,
              icon: product == null ? Icons.add : Icons.save,
            ),
          ],
        ),
      ),
    );
  }
}