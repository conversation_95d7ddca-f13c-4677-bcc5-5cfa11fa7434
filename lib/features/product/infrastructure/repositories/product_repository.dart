import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dartz/dartz.dart';
import 'package:pos_app/core/constants/app_constants.dart';
import 'package:pos_app/core/errors/failures.dart';
import 'package:pos_app/core/utils/logger.dart';
import 'package:pos_app/core/utils/network_info.dart';
import 'package:pos_app/models/product.dart';

abstract class ProductRepository {
  Future<Either<Failure, List<Product>>> getProducts();
  Future<Either<Failure, List<Product>>> getProductsByOutlet(String outletId);
  Future<Either<Failure, Product>> getProduct(String id);
  Future<Either<Failure, Product>> createProduct(Product product);
  Future<Either<Failure, Product>> updateProduct(Product product);
  Future<Either<Failure, void>> deleteProduct(String id);
  Future<Either<Failure, void>> updateStock(String productId, String outletId, int quantity);
}

class ProductRepositoryImpl implements ProductRepository {
  final FirebaseFirestore _firestore;
  final NetworkInfo _networkInfo;

  ProductRepositoryImpl({
    required FirebaseFirestore firestore,
    required NetworkInfo networkInfo,
  })  : _firestore = firestore,
        _networkInfo = networkInfo;

  @override
  Future<Either<Failure, List<Product>>> getProducts() async {
    try {
      final isConnected = await _networkInfo.isConnected;
      if (!isConnected) {
        return const Left(NetworkFailure(AppConstants.networkErrorMessage));
      }

      final querySnapshot = await _firestore
          .collection(AppConstants.productsCollection)
          .orderBy('name')
          .get();

      final products = querySnapshot.docs
          .map((doc) => Product.fromMap(doc.data()))
          .toList();

      return Right(products);
    } catch (e) {
      AppLogger.error('Get products error', e);
      return Left(UnknownFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Product>>> getProductsByOutlet(String outletId) async {
    try {
      final isConnected = await _networkInfo.isConnected;
      if (!isConnected) {
        return const Left(NetworkFailure(AppConstants.networkErrorMessage));
      }

      final querySnapshot = await _firestore
          .collection(AppConstants.productsCollection)
          .where('stock.$outletId', isGreaterThanOrEqualTo: 0)
          .orderBy('stock.$outletId')
          .orderBy('name')
          .get();

      final products = querySnapshot.docs
          .map((doc) => Product.fromMap(doc.data()))
          .toList();

      return Right(products);
    } catch (e) {
      AppLogger.error('Get products by outlet error', e);
      return Left(UnknownFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, Product>> getProduct(String id) async {
    try {
      final isConnected = await _networkInfo.isConnected;
      if (!isConnected) {
        return const Left(NetworkFailure(AppConstants.networkErrorMessage));
      }

      final docSnapshot = await _firestore
          .collection(AppConstants.productsCollection)
          .doc(id)
          .get();

      if (!docSnapshot.exists) {
        return const Left(ValidationFailure('Product not found'));
      }

      final product = Product.fromMap(docSnapshot.data()!);
      return Right(product);
    } catch (e) {
      AppLogger.error('Get product error', e);
      return Left(UnknownFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, Product>> createProduct(Product product) async {
    try {
      final isConnected = await _networkInfo.isConnected;
      if (!isConnected) {
        return const Left(NetworkFailure(AppConstants.networkErrorMessage));
      }

      final docRef = _firestore.collection(AppConstants.productsCollection).doc();
      final newProduct = product.copyWith(
        id: docRef.id,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await docRef.set(newProduct.toMap());
      return Right(newProduct);
    } catch (e) {
      AppLogger.error('Create product error', e);
      return Left(UnknownFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, Product>> updateProduct(Product product) async {
    try {
      final isConnected = await _networkInfo.isConnected;
      if (!isConnected) {
        return const Left(NetworkFailure(AppConstants.networkErrorMessage));
      }

      final updatedProduct = product.copyWith(
        updatedAt: DateTime.now(),
      );

      await _firestore
          .collection(AppConstants.productsCollection)
          .doc(product.id)
          .update(updatedProduct.toMap());

      return Right(updatedProduct);
    } catch (e) {
      AppLogger.error('Update product error', e);
      return Left(UnknownFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> deleteProduct(String id) async {
    try {
      final isConnected = await _networkInfo.isConnected;
      if (!isConnected) {
        return const Left(NetworkFailure(AppConstants.networkErrorMessage));
      }

      await _firestore
          .collection(AppConstants.productsCollection)
          .doc(id)
          .delete();

      return const Right(null);
    } catch (e) {
      AppLogger.error('Delete product error', e);
      return Left(UnknownFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> updateStock(
    String productId,
    String outletId,
    int quantity,
  ) async {
    try {
      final isConnected = await _networkInfo.isConnected;
      if (!isConnected) {
        return const Left(NetworkFailure(AppConstants.networkErrorMessage));
      }

      await _firestore
          .collection(AppConstants.productsCollection)
          .doc(productId)
          .update({
        'stock.$outletId': quantity,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      return const Right(null);
    } catch (e) {
      AppLogger.error('Update stock error', e);
      return Left(UnknownFailure(e.toString()));
    }
  }
}