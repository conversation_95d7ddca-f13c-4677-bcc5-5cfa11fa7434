import 'package:dartz/dartz.dart';
import 'package:pos_app/core/errors/failures.dart';
import 'package:pos_app/models/product.dart';
import 'package:pos_app/repositories/product_repository_http.dart' as http_repo;
import 'product_repository.dart';

/// Adapter to make HTTP repository compatible with existing interface
class ProductRepositoryAdapter implements ProductRepository {
  final http_repo.ProductRepository _httpRepository;

  ProductRepositoryAdapter(this._httpRepository);

  @override
  Future<Either<Failure, List<Product>>> getProducts() async {
    final result = await _httpRepository.getProducts();
    return result.fold(
      (failure) => Left(failure),
      (response) => Right(response),
    );
  }

  @override
  Future<Either<Failure, List<Product>>> getProductsByOutlet(String outletId) async {
    // For HTTP API, we get all products and filter by outlet if needed
    // The backend doesn't have outlet-specific stock in the same way
    return await getProducts();
  }

  @override
  Future<Either<Failure, Product>> getProduct(String id) async {
    return await _httpRepository.getProductById(id);
  }

  @override
  Future<Either<Failure, Product>> createProduct(Product product) async {
    if (_httpRepository is http_repo.ProductRepositoryHttp) {
      return await (_httpRepository as http_repo.ProductRepositoryHttp).createProduct(product);
    }
    return const Left(UnknownFailure('Create product not supported'));
  }

  @override
  Future<Either<Failure, Product>> updateProduct(Product product) async {
    if (_httpRepository is http_repo.ProductRepositoryHttp) {
      return await (_httpRepository as http_repo.ProductRepositoryHttp).updateProduct(product);
    }
    return const Left(UnknownFailure('Update product not supported'));
  }

  @override
  Future<Either<Failure, void>> deleteProduct(String id) async {
    if (_httpRepository is http_repo.ProductRepositoryHttp) {
      return await (_httpRepository as http_repo.ProductRepositoryHttp).deleteProduct(id);
    }
    return const Left(UnknownFailure('Delete product not supported'));
  }

  @override
  Future<Either<Failure, void>> updateStock(String productId, String outletId, int quantity) async {
    if (_httpRepository is http_repo.ProductRepositoryHttp) {
      return await (_httpRepository as http_repo.ProductRepositoryHttp).updateProductStock(productId, quantity);
    }
    return const Left(UnknownFailure('Update stock not supported'));
  }

  // Additional methods from HTTP repository
  Future<Either<Failure, Product>> getProductByBarcode(String barcode) async {
    return await _httpRepository.getProductByBarcode(barcode);
  }

  Future<Either<Failure, List<String>>> getCategories() async {
    return await _httpRepository.getCategories();
  }

  Future<Either<Failure, Map<String, dynamic>>> getProductStats() async {
    return await _httpRepository.getProductStats();
  }

  Future<Either<Failure, List<Product>>> getLowStockProducts({int threshold = 10}) async {
    return await _httpRepository.getLowStockProducts(threshold: threshold);
  }
}
