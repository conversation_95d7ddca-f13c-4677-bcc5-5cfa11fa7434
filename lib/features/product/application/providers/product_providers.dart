import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:pos_app/features/auth/application/providers/auth_providers.dart';
import 'package:pos_app/features/product/application/notifiers/product_notifier.dart';
import 'package:pos_app/features/product/infrastructure/repositories/product_repository.dart';
import 'package:pos_app/features/product/infrastructure/repositories/product_repository_adapter.dart';
import 'package:pos_app/repositories/product_repository_http.dart' as http_repo;

// Repository Provider
final productRepositoryProvider = Provider<ProductRepository>((ref) {
  final httpClient = ref.watch(httpClientServiceProvider);
  final networkInfo = ref.watch(networkInfoProvider);

  final httpRepository = http_repo.ProductRepositoryHttp(
    httpClient: httpClient,
    networkInfo: networkInfo,
  );

  return ProductRepositoryAdapter(httpRepository);
});

// Notifier Providers
final productStateProvider = StateNotifierProvider<ProductNotifier, ProductState>(
  (ref) => ProductNotifier(ref.watch(productRepositoryProvider)),
);

final productFormStateProvider = StateNotifierProvider<ProductFormNotifier, ProductFormState>(
  (ref) => ProductFormNotifier(ref.watch(productRepositoryProvider)),
);