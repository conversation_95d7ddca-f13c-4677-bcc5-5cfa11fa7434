import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:pos_app/features/auth/application/providers/auth_providers.dart';
import 'package:pos_app/features/product/application/notifiers/product_notifier.dart';
import 'package:pos_app/features/product/infrastructure/repositories/product_repository.dart';

// Repository Provider
final productRepositoryProvider = Provider<ProductRepository>((ref) {
  return ProductRepositoryImpl(
    firestore: FirebaseFirestore.instance,
    networkInfo: ref.watch(networkInfoProvider),
  );
});

// Notifier Providers
final productStateProvider = StateNotifierProvider<ProductNotifier, ProductState>(
  (ref) => ProductNotifier(ref.watch(productRepositoryProvider)),
);

final productFormStateProvider = StateNotifierProvider<ProductFormNotifier, ProductFormState>(
  (ref) => ProductFormNotifier(ref.watch(productRepositoryProvider)),
);