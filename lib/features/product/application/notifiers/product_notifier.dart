import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:pos_app/core/utils/logger.dart';
import 'package:pos_app/features/product/infrastructure/repositories/product_repository.dart';
import 'package:pos_app/models/product.dart';

// Product State
abstract class ProductState {
  const ProductState();
}

class ProductInitial extends ProductState {
  const ProductInitial();
}

class ProductLoading extends ProductState {
  const ProductLoading();
}

class ProductSuccess extends ProductState {
  final List<Product> products;
  const ProductSuccess(this.products);
}

class ProductError extends ProductState {
  final String message;
  const ProductError(this.message);
}

// Product Form State
abstract class ProductFormState {
  const ProductFormState();
}

class ProductFormInitial extends ProductFormState {
  const ProductFormInitial();
}

class ProductFormLoading extends ProductFormState {
  const ProductFormLoading();
}

class ProductFormSuccess extends ProductFormState {
  final String message;
  const ProductFormSuccess(this.message);
}

class ProductFormError extends ProductFormState {
  final String message;
  const ProductFormError(this.message);
}

// Product Notifier
class ProductNotifier extends StateNotifier<ProductState> {
  final ProductRepository _repository;

  ProductNotifier(this._repository) : super(const ProductInitial());

  Future<void> getProducts() async {
    state = const ProductLoading();
    final result = await _repository.getProducts();
    result.fold(
      (failure) {
        AppLogger.error('Get products failed: ${failure.message}');
        state = ProductError(failure.message);
      },
      (products) {
        AppLogger.info('Get products success: ${products.length} products');
        state = ProductSuccess(products);
      },
    );
  }

  Future<void> getProductsByOutlet(String outletId) async {
    state = const ProductLoading();
    final result = await _repository.getProductsByOutlet(outletId);
    result.fold(
      (failure) {
        AppLogger.error('Get products by outlet failed: ${failure.message}');
        state = ProductError(failure.message);
      },
      (products) {
        AppLogger.info('Get products by outlet success: ${products.length} products');
        state = ProductSuccess(products);
      },
    );
  }
}

// Product Form Notifier
class ProductFormNotifier extends StateNotifier<ProductFormState> {
  final ProductRepository _repository;

  ProductFormNotifier(this._repository) : super(const ProductFormInitial());

  Future<void> createProduct(Product product) async {
    state = const ProductFormLoading();
    final result = await _repository.createProduct(product);
    result.fold(
      (failure) {
        AppLogger.error('Create product failed: ${failure.message}');
        state = ProductFormError(failure.message);
      },
      (product) {
        AppLogger.info('Create product success: ${product.name}');
        state = const ProductFormSuccess('Product created successfully');
      },
    );
  }

  Future<void> updateProduct(Product product) async {
    state = const ProductFormLoading();
    final result = await _repository.updateProduct(product);
    result.fold(
      (failure) {
        AppLogger.error('Update product failed: ${failure.message}');
        state = ProductFormError(failure.message);
      },
      (product) {
        AppLogger.info('Update product success: ${product.name}');
        state = const ProductFormSuccess('Product updated successfully');
      },
    );
  }

  Future<void> deleteProduct(String id) async {
    state = const ProductFormLoading();
    final result = await _repository.deleteProduct(id);
    result.fold(
      (failure) {
        AppLogger.error('Delete product failed: ${failure.message}');
        state = ProductFormError(failure.message);
      },
      (_) {
        AppLogger.info('Delete product success');
        state = const ProductFormSuccess('Product deleted successfully');
      },
    );
  }

  Future<void> updateStock(String productId, String outletId, int quantity) async {
    state = const ProductFormLoading();
    final result = await _repository.updateStock(productId, outletId, quantity);
    result.fold(
      (failure) {
        AppLogger.error('Update stock failed: ${failure.message}');
        state = ProductFormError(failure.message);
      },
      (_) {
        AppLogger.info('Update stock success');
        state = const ProductFormSuccess('Stock updated successfully');
      },
    );
  }
}