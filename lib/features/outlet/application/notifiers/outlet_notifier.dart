import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:pos_app/core/utils/logger.dart';
import 'package:pos_app/features/outlet/infrastructure/repositories/outlet_repository.dart';
import 'package:pos_app/models/outlet.dart';

// Outlet State
abstract class OutletState {
  const OutletState();

  T when<T>({
    required T Function(OutletInitial) initial,
    required T Function(OutletLoading) loading,
    required T Function(OutletSuccess) success,
    required T Function(OutletError) error,
  }) {
    if (this is OutletInitial) {
      return initial(this as OutletInitial);
    } else if (this is OutletLoading) {
      return loading(this as OutletLoading);
    } else if (this is OutletSuccess) {
      return success(this as OutletSuccess);
    } else if (this is OutletError) {
      return error(this as OutletError);
    }
    throw Exception('Unhandled state type: $this');
  }

  T maybeWhen<T>({
    T Function(OutletInitial)? initial,
    T Function(OutletLoading)? loading,
    T Function(OutletSuccess)? success,
    T Function(OutletError)? error,
    required T Function() orElse,
  }) {
    if (this is OutletInitial && initial != null) {
      return initial(this as OutletInitial);
    } else if (this is OutletLoading && loading != null) {
      return loading(this as OutletLoading);
    } else if (this is OutletSuccess && success != null) {
      return success(this as OutletSuccess);
    } else if (this is OutletError && error != null) {
      return error(this as OutletError);
    }
    return orElse();
  }
}

class OutletInitial extends OutletState {
  const OutletInitial();
}

class OutletLoading extends OutletState {
  const OutletLoading();
}

class OutletSuccess extends OutletState {
  final List<Outlet> outlets;
  const OutletSuccess(this.outlets);
}

class OutletError extends OutletState {
  final String message;
  const OutletError(this.message);
}

// Outlet Form State
abstract class OutletFormState {
  const OutletFormState();

  T when<T>({
    required T Function(OutletFormInitial) initial,
    required T Function(OutletFormLoading) loading,
    required T Function(OutletFormSuccess) success,
    required T Function(OutletFormError) error,
  }) {
    if (this is OutletFormInitial) {
      return initial(this as OutletFormInitial);
    } else if (this is OutletFormLoading) {
      return loading(this as OutletFormLoading);
    } else if (this is OutletFormSuccess) {
      return success(this as OutletFormSuccess);
    } else if (this is OutletFormError) {
      return error(this as OutletFormError);
    }
    throw Exception('Unhandled state type: $this');
  }

  T maybeWhen<T>({
    T Function(OutletFormInitial)? initial,
    T Function(OutletFormLoading)? loading,
    T Function(OutletFormSuccess)? success,
    T Function(OutletFormError)? error,
    required T Function() orElse,
  }) {
    if (this is OutletFormInitial && initial != null) {
      return initial(this as OutletFormInitial);
    } else if (this is OutletFormLoading && loading != null) {
      return loading(this as OutletFormLoading);
    } else if (this is OutletFormSuccess && success != null) {
      return success(this as OutletFormSuccess);
    } else if (this is OutletFormError && error != null) {
      return error(this as OutletFormError);
    }
    return orElse();
  }
}

class OutletFormInitial extends OutletFormState {
  const OutletFormInitial();
}

class OutletFormLoading extends OutletFormState {
  const OutletFormLoading();
}

class OutletFormSuccess extends OutletFormState {
  final String message;
  const OutletFormSuccess(this.message);
}

class OutletFormError extends OutletFormState {
  final String message;
  const OutletFormError(this.message);
}

// Outlet Notifier
class OutletNotifier extends StateNotifier<OutletState> {
  final OutletRepository _repository;

  OutletNotifier(this._repository) : super(const OutletInitial());

  Future<void> getOutlets() async {
    state = const OutletLoading();
    final result = await _repository.getOutlets();
    result.fold(
      (failure) {
        AppLogger.error('Get outlets failed: ${failure.message}');
        state = OutletError(failure.message);
      },
      (outlets) {
        AppLogger.info('Get outlets success: ${outlets.length} outlets');
        state = OutletSuccess(outlets);
      },
    );
  }
}

// Outlet Form Notifier
class OutletFormNotifier extends StateNotifier<OutletFormState> {
  final OutletRepository _repository;

  OutletFormNotifier(this._repository) : super(const OutletFormInitial());

  Future<void> createOutlet(Outlet outlet) async {
    state = const OutletFormLoading();
    final result = await _repository.createOutlet(outlet);
    result.fold(
      (failure) {
        AppLogger.error('Create outlet failed: ${failure.message}');
        state = OutletFormError(failure.message);
      },
      (outlet) {
        AppLogger.info('Create outlet success: ${outlet.name}');
        state = const OutletFormSuccess('Outlet created successfully');
      },
    );
  }

  Future<void> updateOutlet(Outlet outlet) async {
    state = const OutletFormLoading();
    final result = await _repository.updateOutlet(outlet);
    result.fold(
      (failure) {
        AppLogger.error('Update outlet failed: ${failure.message}');
        state = OutletFormError(failure.message);
      },
      (outlet) {
        AppLogger.info('Update outlet success: ${outlet.name}');
        state = const OutletFormSuccess('Outlet updated successfully');
      },
    );
  }

  Future<void> deleteOutlet(String id) async {
    state = const OutletFormLoading();
    final result = await _repository.deleteOutlet(id);
    result.fold(
      (failure) {
        AppLogger.error('Delete outlet failed: ${failure.message}');
        state = OutletFormError(failure.message);
      },
      (_) {
        AppLogger.info('Delete outlet success');
        state = const OutletFormSuccess('Outlet deleted successfully');
      },
    );
  }
}