import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:pos_app/features/auth/application/providers/auth_providers.dart';
import 'package:pos_app/features/outlet/application/notifiers/outlet_notifier.dart';
import 'package:pos_app/features/outlet/infrastructure/repositories/outlet_repository.dart';

// Repository Provider (Placeholder - will be implemented later)
final outletRepositoryProvider = Provider<OutletRepository>((ref) {
  // TODO: Implement HTTP-based outlet repository
  throw UnimplementedError('Outlet repository not yet migrated to HTTP');
});

// Notifier Providers
final outletStateProvider = StateNotifierProvider<OutletNotifier, OutletState>(
  (ref) => OutletNotifier(ref.watch(outletRepositoryProvider)),
);

final outletFormStateProvider = StateNotifierProvider<OutletFormNotifier, OutletFormState>(
  (ref) => OutletFormNotifier(ref.watch(outletRepositoryProvider)),
);