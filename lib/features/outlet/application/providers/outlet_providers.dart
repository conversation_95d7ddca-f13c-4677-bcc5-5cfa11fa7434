import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:pos_app/features/auth/application/providers/auth_providers.dart';
import 'package:pos_app/features/outlet/application/notifiers/outlet_notifier.dart';
import 'package:pos_app/features/outlet/infrastructure/repositories/outlet_repository.dart';

// Repository Provider
final outletRepositoryProvider = Provider<OutletRepository>((ref) {
  return OutletRepositoryImpl(
    firestore: FirebaseFirestore.instance,
    networkInfo: ref.watch(networkInfoProvider),
  );
});

// Notifier Providers
final outletStateProvider = StateNotifierProvider<OutletNotifier, OutletState>(
  (ref) => OutletNotifier(ref.watch(outletRepositoryProvider)),
);

final outletFormStateProvider = StateNotifierProvider<OutletFormNotifier, OutletFormState>(
  (ref) => OutletFormNotifier(ref.watch(outletRepositoryProvider)),
);