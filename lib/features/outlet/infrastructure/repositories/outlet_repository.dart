import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dartz/dartz.dart';
import 'package:pos_app/core/constants/app_constants.dart';
import 'package:pos_app/core/errors/failures.dart';
import 'package:pos_app/core/utils/logger.dart';
import 'package:pos_app/core/utils/network_info.dart';
import 'package:pos_app/models/outlet.dart';

abstract class OutletRepository {
  Future<Either<Failure, List<Outlet>>> getOutlets();
  Future<Either<Failure, Outlet>> getOutlet(String id);
  Future<Either<Failure, Outlet>> createOutlet(Outlet outlet);
  Future<Either<Failure, Outlet>> updateOutlet(Outlet outlet);
  Future<Either<Failure, void>> deleteOutlet(String id);
}

class OutletRepositoryImpl implements OutletRepository {
  final FirebaseFirestore _firestore;
  final NetworkInfo _networkInfo;

  OutletRepositoryImpl({
    required FirebaseFirestore firestore,
    required NetworkInfo networkInfo,
  })  : _firestore = firestore,
        _networkInfo = networkInfo;

  @override
  Future<Either<Failure, List<Outlet>>> getOutlets() async {
    try {
      final isConnected = await _networkInfo.isConnected;
      if (!isConnected) {
        return const Left(NetworkFailure(AppConstants.networkErrorMessage));
      }

      final querySnapshot = await _firestore
          .collection(AppConstants.outletsCollection)
          .orderBy('name')
          .get();

      final outlets = querySnapshot.docs
          .map((doc) => Outlet.fromMap(doc.data()))
          .toList();

      return Right(outlets);
    } catch (e) {
      AppLogger.error('Get outlets error', e);
      return Left(UnknownFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, Outlet>> getOutlet(String id) async {
    try {
      final isConnected = await _networkInfo.isConnected;
      if (!isConnected) {
        return const Left(NetworkFailure(AppConstants.networkErrorMessage));
      }

      final docSnapshot = await _firestore
          .collection(AppConstants.outletsCollection)
          .doc(id)
          .get();

      if (!docSnapshot.exists) {
        return const Left(ValidationFailure('Outlet not found'));
      }

      final outlet = Outlet.fromMap(docSnapshot.data()!);
      return Right(outlet);
    } catch (e) {
      AppLogger.error('Get outlet error', e);
      return Left(UnknownFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, Outlet>> createOutlet(Outlet outlet) async {
    try {
      final isConnected = await _networkInfo.isConnected;
      if (!isConnected) {
        return const Left(NetworkFailure(AppConstants.networkErrorMessage));
      }

      final docRef = _firestore.collection(AppConstants.outletsCollection).doc();
      final newOutlet = outlet.copyWith(
        id: docRef.id,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await docRef.set(newOutlet.toMap());
      return Right(newOutlet);
    } catch (e) {
      AppLogger.error('Create outlet error', e);
      return Left(UnknownFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, Outlet>> updateOutlet(Outlet outlet) async {
    try {
      final isConnected = await _networkInfo.isConnected;
      if (!isConnected) {
        return const Left(NetworkFailure(AppConstants.networkErrorMessage));
      }

      final updatedOutlet = outlet.copyWith(
        updatedAt: DateTime.now(),
      );

      await _firestore
          .collection(AppConstants.outletsCollection)
          .doc(outlet.id)
          .update(updatedOutlet.toMap());

      return Right(updatedOutlet);
    } catch (e) {
      AppLogger.error('Update outlet error', e);
      return Left(UnknownFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> deleteOutlet(String id) async {
    try {
      final isConnected = await _networkInfo.isConnected;
      if (!isConnected) {
        return const Left(NetworkFailure(AppConstants.networkErrorMessage));
      }

      await _firestore
          .collection(AppConstants.outletsCollection)
          .doc(id)
          .delete();

      return const Right(null);
    } catch (e) {
      AppLogger.error('Delete outlet error', e);
      return Left(UnknownFailure(e.toString()));
    }
  }
}