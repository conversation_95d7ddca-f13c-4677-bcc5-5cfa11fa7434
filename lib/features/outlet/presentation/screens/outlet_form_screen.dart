import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:geolocator/geolocator.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:pos_app/core/config/app_theme.dart';
import 'package:pos_app/core/constants/app_constants.dart';
import 'package:pos_app/core/widgets/app_button.dart';
import 'package:pos_app/features/outlet/application/notifiers/outlet_notifier.dart';
import 'package:pos_app/features/outlet/application/providers/outlet_providers.dart';
import 'package:pos_app/models/outlet.dart';

class OutletFormScreen extends HookConsumerWidget {
  static const String routeName = '/outlet-form';

  final Outlet? outlet;

  const OutletFormScreen({
    super.key,
    this.outlet,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = useMemoized(() => GlobalKey<FormState>());
    final nameController = useTextEditingController(text: outlet?.name);
    final addressController = useTextEditingController(text: outlet?.address);
    final phoneController = useTextEditingController(text: outlet?.phone);
    final location = useState<Position?>(null);
    final isLoading = useState(false);

    // Watch form state
    final formState = ref.watch(outletFormStateProvider);

    // Handle form state changes
    ref.listen(outletFormStateProvider, (previous, next) {
      if (next is OutletFormSuccess) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(next.message),
            backgroundColor: AppTheme.successColor,
          ),
        );
        Navigator.pop(context);
      } else if (next is OutletFormError) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(next.message),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    });

    // Get current location
    Future<void> getCurrentLocation() async {
      isLoading.value = true;
      try {
        final permission = await Geolocator.checkPermission();
        if (permission == LocationPermission.denied) {
          await Geolocator.requestPermission();
        }
        
        final currentPosition = await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.high,
        );
        
        location.value = currentPosition;
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to get location'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      } finally {
        isLoading.value = false;
      }
    }

    // Handle form submission
    Future<void> handleSubmit() async {
      if (formKey.currentState?.validate() ?? false) {
        if (location.value == null) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Please get location first'),
              backgroundColor: AppTheme.errorColor,
            ),
          );
          return;
        }

        final newOutlet = (outlet ?? Outlet.empty()).copyWith(
          name: nameController.text,
          address: addressController.text,
          phone: phoneController.text,
          location: GeoPoint(
            location.value!.latitude,
            location.value!.longitude,
          ),
        );

        if (outlet == null) {
          await ref.read(outletFormStateProvider.notifier).createOutlet(newOutlet);
        } else {
          await ref.read(outletFormStateProvider.notifier).updateOutlet(newOutlet);
        }
      }
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(outlet == null ? 'Add Outlet' : 'Edit Outlet'),
      ),
      body: Form(
        key: formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            TextFormField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'Outlet Name',
                hintText: 'Enter outlet name',
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return AppConstants.requiredFieldMessage;
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: addressController,
              decoration: const InputDecoration(
                labelText: 'Address',
                hintText: 'Enter outlet address',
              ),
              maxLines: 3,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return AppConstants.requiredFieldMessage;
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: phoneController,
              decoration: const InputDecoration(
                labelText: 'Phone',
                hintText: 'Enter outlet phone',
              ),
              keyboardType: TextInputType.phone,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return AppConstants.requiredFieldMessage;
                }
                if (!RegExp(r'^\+?[\d\s-]+$').hasMatch(value)) {
                  return AppConstants.invalidPhoneMessage;
                }
                return null;
              },
            ),
            const SizedBox(height: 24),
            AppButton(
              label: 'Get Current Location',
              onPressed: getCurrentLocation,
              isLoading: isLoading.value,
              type: AppButtonType.secondary,
              icon: Icons.location_on,
            ),
            if (location.value != null) ...[
              const SizedBox(height: 16),
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Location',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Latitude: ${location.value!.latitude}',
                        style: const TextStyle(fontSize: 14),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Longitude: ${location.value!.longitude}',
                        style: const TextStyle(fontSize: 14),
                      ),
                    ],
                  ),
                ),
              ),
            ],
            const SizedBox(height: 24),
            AppButton(
              label: outlet == null ? 'Create Outlet' : 'Update Outlet',
              onPressed: handleSubmit,
              isLoading: formState is OutletFormLoading,
              icon: outlet == null ? Icons.add : Icons.save,
            ),
          ],
        ),
      ),
    );
  }
}