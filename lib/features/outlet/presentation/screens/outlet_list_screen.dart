import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:pos_app/core/config/app_theme.dart';
import 'package:pos_app/core/widgets/app_error.dart';
import 'package:pos_app/core/widgets/app_loading.dart';
import 'package:pos_app/features/outlet/application/notifiers/outlet_notifier.dart';
import 'package:pos_app/features/outlet/application/providers/outlet_providers.dart';
import 'package:pos_app/features/outlet/presentation/screens/outlet_form_screen.dart';
import 'package:pos_app/models/outlet.dart';

class OutletListScreen extends HookConsumerWidget {
  static const String routeName = '/outlets';

  const OutletListScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final outletState = ref.watch(outletStateProvider);

    // Load outlets on init
    useEffect(() {
      ref.read(outletStateProvider.notifier).getOutlets();
      return null;
    }, []);

    // Handle outlet state changes
    ref.listen(outletStateProvider, (previous, next) {
      if (next is OutletSuccess) {
        // Refresh list completed
      } else if (next is OutletError) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(next.message),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    });

    return Scaffold(
      appBar: AppBar(
        title: const Text('Outlets'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Builder(
        builder: (context) {
          if (outletState is OutletLoading) {
            return const AppLoading();
          }

          if (outletState is OutletError) {
            return AppError(
              message: outletState.message,
              onRetry: () {
                ref.read(outletStateProvider.notifier).getOutlets();
              },
            );
          }

          if (outletState is OutletSuccess) {
            return _OutletList(outlets: outletState.outlets);
          }

          return const SizedBox.shrink();
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.pushNamed(context, OutletFormScreen.routeName);
        },
        backgroundColor: AppTheme.primaryColor,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }
}

class _OutletList extends StatelessWidget {
  final List<Outlet> outlets;

  const _OutletList({required this.outlets});

  @override
  Widget build(BuildContext context) {
    if (outlets.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.store_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No outlets found',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Add outlets to get started',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: outlets.length,
      itemBuilder: (context, index) {
        final outlet = outlets[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    // Outlet Icon
                    Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        color: AppTheme.primaryColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.store,
                        color: AppTheme.primaryColor,
                        size: 32,
                      ),
                    ),
                    const SizedBox(width: 16),
                    
                    // Outlet Info
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            outlet.name,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 18,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 2,
                                ),
                                decoration: BoxDecoration(
                                  color: outlet.isActive
                                      ? AppTheme.successColor.withOpacity(0.1)
                                      : AppTheme.errorColor.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Text(
                                  outlet.isActive ? 'Active' : 'Inactive',
                                  style: TextStyle(
                                    color: outlet.isActive
                                        ? AppTheme.successColor
                                        : AppTheme.errorColor,
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    
                    // Actions
                    PopupMenuButton(
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'edit',
                          child: Row(
                            children: [
                              Icon(Icons.edit),
                              SizedBox(width: 8),
                              Text('Edit'),
                            ],
                          ),
                        ),
                        PopupMenuItem(
                          value: outlet.isActive ? 'deactivate' : 'activate',
                          child: Row(
                            children: [
                              Icon(
                                outlet.isActive ? Icons.block : Icons.check_circle,
                                color: outlet.isActive ? Colors.orange : Colors.green,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                outlet.isActive ? 'Deactivate' : 'Activate',
                                style: TextStyle(
                                  color: outlet.isActive ? Colors.orange : Colors.green,
                                ),
                              ),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              Icon(Icons.delete, color: Colors.red),
                              SizedBox(width: 8),
                              Text('Delete', style: TextStyle(color: Colors.red)),
                            ],
                          ),
                        ),
                      ],
                      onSelected: (value) {
                        if (value == 'edit') {
                          Navigator.pushNamed(
                            context,
                            OutletFormScreen.routeName,
                            arguments: outlet,
                          );
                        } else if (value == 'deactivate' || value == 'activate') {
                          _showStatusChangeDialog(context, outlet, value == 'activate');
                        } else if (value == 'delete') {
                          _showDeleteDialog(context, outlet);
                        }
                      },
                    ),
                  ],
                ),
                
                const SizedBox(height: 12),
                
                // Address
                Row(
                  children: [
                    const Icon(Icons.location_on, size: 16, color: Colors.grey),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        outlet.address,
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 8),
                
                // Phone
                Row(
                  children: [
                    const Icon(Icons.phone, size: 16, color: Colors.grey),
                    const SizedBox(width: 4),
                    Text(
                      outlet.phone,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 8),
                
                // Location coordinates
                Row(
                  children: [
                    const Icon(Icons.gps_fixed, size: 16, color: Colors.grey),
                    const SizedBox(width: 4),
                    Text(
                      'Lat: ${outlet.location.latitude.toStringAsFixed(6)}, '
                      'Lng: ${outlet.location.longitude.toStringAsFixed(6)}',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showStatusChangeDialog(BuildContext context, Outlet outlet, bool activate) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('${activate ? 'Activate' : 'Deactivate'} Outlet'),
        content: Text(
          'Are you sure you want to ${activate ? 'activate' : 'deactivate'} ${outlet.name}?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              // TODO: Implement status change functionality
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Status change functionality will be implemented'),
                  backgroundColor: AppTheme.warningColor,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: activate ? AppTheme.successColor : AppTheme.warningColor,
              foregroundColor: Colors.white,
            ),
            child: Text(activate ? 'Activate' : 'Deactivate'),
          ),
        ],
      ),
    );
  }

  void _showDeleteDialog(BuildContext context, Outlet outlet) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Outlet'),
        content: Text(
          'Are you sure you want to delete ${outlet.name}? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              // TODO: Implement delete functionality
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Delete functionality will be implemented'),
                  backgroundColor: AppTheme.warningColor,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}