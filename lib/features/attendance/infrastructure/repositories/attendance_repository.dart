import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dartz/dartz.dart';
import 'package:hive/hive.dart';
import 'package:pos_app/core/constants/app_constants.dart';
import 'package:pos_app/core/errors/failures.dart';
import 'package:pos_app/core/utils/logger.dart';
import 'package:pos_app/core/utils/network_info.dart';
import 'package:pos_app/models/attendance.dart';

abstract class AttendanceRepository {
  Future<Either<Failure, List<Attendance>>> getAttendances(String employeeId);
  Future<Either<Failure, Attendance>> createAttendance(Attendance attendance);
  Future<Either<Failure, Attendance>> updateAttendance(
    String employeeId,
    GeoPoint location,
  );
  Future<Either<Failure, void>> syncOfflineAttendances();
}

class AttendanceRepositoryImpl implements AttendanceRepository {
  final FirebaseFirestore _firestore;
  final NetworkInfo _networkInfo;
  final Box _attendanceBox;

  AttendanceRepositoryImpl({
    required FirebaseFirestore firestore,
    required NetworkInfo networkInfo,
    required Box attendanceBox,
  })  : _firestore = firestore,
        _networkInfo = networkInfo,
        _attendanceBox = attendanceBox;

  @override
  Future<Either<Failure, List<Attendance>>> getAttendances(
    String employeeId,
  ) async {
    try {
      final isConnected = await _networkInfo.isConnected;

      if (!isConnected) {
        // Get from local storage
        final attendances = _attendanceBox.values
            .map((data) => Attendance.fromMap(Map<String, dynamic>.from(data)))
            .where((attendance) => attendance.employeeId == employeeId)
            .toList();
        return Right(attendances);
      }

      final querySnapshot = await _firestore
          .collection(AppConstants.attendanceCollection)
          .where('employeeId', isEqualTo: employeeId)
          .orderBy('clockInTime', descending: true)
          .get();

      final attendances = querySnapshot.docs
          .map((doc) => Attendance.fromMap(doc.data()))
          .toList();

      return Right(attendances);
    } catch (e) {
      AppLogger.error('Get attendances error', e);
      return Left(UnknownFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, Attendance>> createAttendance(
    Attendance attendance,
  ) async {
    try {
      final isConnected = await _networkInfo.isConnected;

      if (!isConnected) {
        // Save offline
        final docId = _firestore.collection(AppConstants.attendanceCollection).doc().id;
        final newAttendance = attendance.copyWith(id: docId);
        await _attendanceBox.add(newAttendance.toMap());
        return Right(newAttendance);
      }

      // Save online
      final docRef = _firestore.collection(AppConstants.attendanceCollection).doc();
      final newAttendance = attendance.copyWith(
        id: docRef.id,
        isSynced: true,
      );

      await docRef.set(newAttendance.toMap());
      return Right(newAttendance);
    } catch (e) {
      AppLogger.error('Create attendance error', e);
      return Left(UnknownFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, Attendance>> updateAttendance(
    String employeeId,
    GeoPoint location,
  ) async {
    try {
      final isConnected = await _networkInfo.isConnected;

      if (!isConnected) {
        // Update offline
        final attendance = _attendanceBox.values
            .map((data) => Attendance.fromMap(Map<String, dynamic>.from(data)))
            .where((a) => a.employeeId == employeeId && !a.isComplete)
            .firstOrNull;

        if (attendance == null) {
          return const Left(ValidationFailure('No active attendance found'));
        }

        final updatedAttendance = attendance.copyWith(
          checkOutTime: DateTime.now(),
          checkOutLocation: AttendanceLocation(
            latitude: location.latitude,
            longitude: location.longitude,
          ),
        );

        await _attendanceBox.put(attendance.id, updatedAttendance.toMap());
        return Right(updatedAttendance);
      }

      // Update online
      final querySnapshot = await _firestore
          .collection(AppConstants.attendanceCollection)
          .where('employeeId', isEqualTo: employeeId)
          .where('clockOutTime', isNull: true)
          .limit(1)
          .get();

      if (querySnapshot.docs.isEmpty) {
        return const Left(ValidationFailure('No active attendance found'));
      }

      final doc = querySnapshot.docs.first;
      final attendance = Attendance.fromMap(doc.data());
      final updatedAttendance = attendance.copyWith(
        checkOutTime: DateTime.now(),
        checkOutLocation: AttendanceLocation(
          latitude: location.latitude,
          longitude: location.longitude,
        ),
      );

      await doc.reference.update(updatedAttendance.toMap());
      return Right(updatedAttendance);
    } catch (e) {
      AppLogger.error('Update attendance error', e);
      return Left(UnknownFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> syncOfflineAttendances() async {
    try {
      final isConnected = await _networkInfo.isConnected;
      if (!isConnected) {
        return const Left(NetworkFailure(AppConstants.networkErrorMessage));
      }

      final offlineAttendances = _attendanceBox.values
          .map((data) => Attendance.fromMap(Map<String, dynamic>.from(data)))
          .where((attendance) => !attendance.isSynced)
          .toList();

      for (final attendance in offlineAttendances) {
        final docRef = _firestore
            .collection(AppConstants.attendanceCollection)
            .doc(attendance.id);

        await docRef.set(
          attendance.copyWith(isSynced: true).toMap(),
        );

        await _attendanceBox.delete(attendance.id);
      }

      return const Right(null);
    } catch (e) {
      AppLogger.error('Sync offline attendances error', e);
      return Left(UnknownFailure(e.toString()));
    }
  }
}