import 'package:dartz/dartz.dart';
import 'package:hive/hive.dart';
import 'package:pos_app/core/errors/failures.dart';
import 'package:pos_app/core/utils/network_info.dart';
import 'package:pos_app/models/attendance.dart';
import 'attendance_repository.dart';

/// Placeholder implementation for AttendanceRepository
/// This will be replaced with HTTP-based repository later
class AttendanceRepositoryPlaceholder implements AttendanceRepository {
  final NetworkInfo _networkInfo;
  final Box _attendanceBox;

  AttendanceRepositoryPlaceholder({
    required NetworkInfo networkInfo,
    required Box attendanceBox,
  })  : _networkInfo = networkInfo,
        _attendanceBox = attendanceBox;

  @override
  Future<Either<Failure, Attendance>> createAttendance(Attendance attendance) async {
    // Placeholder implementation - save to local storage only
    try {
      final newAttendance = attendance.copyWith(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        isSynced: false,
        createdAt: DateTime.now(),
      );

      await _attendanceBox.add(newAttendance.toMap());
      return Right(newAttendance);
    } catch (e) {
      return Left(UnknownFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, Attendance>> updateAttendance(
    String employeeId,
    dynamic location, // Using dynamic to avoid GeoPoint dependency
  ) async {
    // Placeholder implementation
    try {
      // Find attendance in local storage
      final attendanceData = _attendanceBox.values
          .cast<Map<String, dynamic>>()
          .firstWhere(
            (data) => data['employeeId'] == employeeId && data['checkOutTime'] == null,
            orElse: () => <String, dynamic>{},
          );

      if (attendanceData.isEmpty) {
        return const Left(NotFoundFailure('No active attendance found'));
      }

      final attendance = Attendance.fromMap(attendanceData);
      final updatedAttendance = attendance.copyWith(
        checkOutTime: DateTime.now(),
        checkOutLocation: const AttendanceLocation(latitude: 0, longitude: 0), // Placeholder location
      );

      // Update in local storage
      final index = _attendanceBox.values.toList().indexWhere(
        (data) => (data as Map<String, dynamic>)['id'] == attendance.id,
      );

      if (index != -1) {
        await _attendanceBox.putAt(index, updatedAttendance.toMap());
      }

      return Right(updatedAttendance);
    } catch (e) {
      return Left(UnknownFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Attendance>>> getAttendances(String employeeId) async {
    // Placeholder implementation - return from local storage
    try {
      final attendances = _attendanceBox.values
          .cast<Map<String, dynamic>>()
          .map((data) => Attendance.fromMap(data))
          .where((attendance) => attendance.employeeId == employeeId)
          .toList();

      return Right(attendances);
    } catch (e) {
      return Left(UnknownFailure(e.toString()));
    }
  }

  // Additional helper method (not in interface)
  Future<Either<Failure, Attendance?>> getTodayAttendance(String employeeId) async {
    // Placeholder implementation
    try {
      final today = DateTime.now();
      final startOfDay = DateTime(today.year, today.month, today.day);
      final endOfDay = startOfDay.add(const Duration(days: 1));

      final attendances = _attendanceBox.values
          .cast<Map<String, dynamic>>()
          .map((data) => Attendance.fromMap(data))
          .where((attendance) =>
              attendance.employeeId == employeeId &&
              attendance.checkInTime.isAfter(startOfDay) &&
              attendance.checkInTime.isBefore(endOfDay))
          .toList();

      return Right(attendances.isNotEmpty ? attendances.first : null);
    } catch (e) {
      return Left(UnknownFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> syncOfflineAttendances() async {
    // Placeholder implementation - just mark as synced
    try {
      final unsynced = _attendanceBox.values
          .cast<Map<String, dynamic>>()
          .where((data) => data['isSynced'] == false)
          .toList();

      for (int i = 0; i < unsynced.length; i++) {
        final data = Map<String, dynamic>.from(unsynced[i]);
        data['isSynced'] = true;

        final index = _attendanceBox.values.toList().indexWhere(
          (boxData) => (boxData as Map<String, dynamic>)['id'] == data['id'],
        );

        if (index != -1) {
          await _attendanceBox.putAt(index, data);
        }
      }

      return const Right(null);
    } catch (e) {
      return Left(UnknownFailure(e.toString()));
    }
  }
}
