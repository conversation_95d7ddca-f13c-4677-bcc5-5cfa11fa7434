import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:geolocator/geolocator.dart';
import 'package:pos_app/core/constants/app_constants.dart';
import 'package:pos_app/core/utils/logger.dart';
import 'package:pos_app/features/attendance/infrastructure/repositories/attendance_repository.dart';
import 'package:pos_app/models/attendance.dart';
import 'package:pos_app/models/outlet.dart';

// Attendance State
abstract class AttendanceState {
  const AttendanceState();
}

class AttendanceInitial extends AttendanceState {
  const AttendanceInitial();
}

class AttendanceLoading extends AttendanceState {
  const AttendanceLoading();
}

class AttendanceSuccess extends AttendanceState {
  final String message;
  const AttendanceSuccess(this.message);
}

class AttendanceError extends AttendanceState {
  final String message;
  const AttendanceError(this.message);
}

// Attendance List State
abstract class AttendanceListState {
  const AttendanceListState();
}

class AttendanceListInitial extends AttendanceListState {
  const AttendanceListInitial();
}

class AttendanceListLoading extends AttendanceListState {
  const AttendanceListLoading();
}

class AttendanceListSuccess extends AttendanceListState {
  final List<Attendance> attendances;
  const AttendanceListSuccess(this.attendances);
}

class AttendanceListError extends AttendanceListState {
  final String message;
  const AttendanceListError(this.message);
}

// Attendance Notifier
class AttendanceNotifier extends StateNotifier<AttendanceState> {
  final AttendanceRepository _repository;

  AttendanceNotifier(this._repository) : super(const AttendanceInitial());

  Future<void> checkIn(String employeeId, Outlet outlet) async {
    try {
      state = const AttendanceLoading();

      // Check location permission
      final permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        final requested = await Geolocator.requestPermission();
        if (requested == LocationPermission.denied) {
          state = const AttendanceError('Location permission denied');
          return;
        }
      }

      // Get current location
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      // Calculate distance from outlet
      final distance = Geolocator.distanceBetween(
        position.latitude,
        position.longitude,
        outlet.location.latitude,
        outlet.location.longitude,
      );

      // Validate distance
      if (distance > AppConstants.maxLocationDistanceMeters) {
        state = const AttendanceError(
          'You are too far from the outlet to check in',
        );
        return;
      }

      // Create attendance record
      final attendance = Attendance(
        id: '',
        employeeId: employeeId,
        outletId: outlet.id,
        clockInTime: DateTime.now(),
        clockInLocation: outlet.location,
        clockOutTime: null,
        clockOutLocation: null,
        isSynced: false,
      );

      final result = await _repository.createAttendance(attendance);

      result.fold(
        (failure) {
          AppLogger.error('Check in failed: ${failure.message}');
          state = AttendanceError(failure.message);
        },
        (attendance) {
          AppLogger.info('Check in success: ${attendance.id}');
          state = const AttendanceSuccess('Check in successful');
        },
      );
    } catch (e) {
      AppLogger.error('Check in error', e);
      state = AttendanceError(e.toString());
    }
  }

  Future<void> checkOut(String employeeId, Outlet outlet) async {
    try {
      state = const AttendanceLoading();

      // Get current location
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      // Calculate distance from outlet
      final distance = Geolocator.distanceBetween(
        position.latitude,
        position.longitude,
        outlet.location.latitude,
        outlet.location.longitude,
      );

      // Validate distance
      if (distance > AppConstants.maxLocationDistanceMeters) {
        state = const AttendanceError(
          'You are too far from the outlet to check out',
        );
        return;
      }

      final result = await _repository.updateAttendance(
        employeeId,
        outlet.location,
      );

      result.fold(
        (failure) {
          AppLogger.error('Check out failed: ${failure.message}');
          state = AttendanceError(failure.message);
        },
        (attendance) {
          AppLogger.info('Check out success: ${attendance.id}');
          state = const AttendanceSuccess('Check out successful');
        },
      );
    } catch (e) {
      AppLogger.error('Check out error', e);
      state = AttendanceError(e.toString());
    }
  }
}

// Attendance List Notifier
class AttendanceListNotifier extends StateNotifier<AttendanceListState> {
  final AttendanceRepository _repository;

  AttendanceListNotifier(this._repository)
      : super(const AttendanceListInitial());

  Future<void> getAttendances(String employeeId) async {
    state = const AttendanceListLoading();

    final result = await _repository.getAttendances(employeeId);

    result.fold(
      (failure) {
        AppLogger.error('Get attendances failed: ${failure.message}');
        state = AttendanceListError(failure.message);
      },
      (attendances) {
        AppLogger.info('Get attendances success: ${attendances.length}');
        state = AttendanceListSuccess(attendances);
      },
    );
  }
}