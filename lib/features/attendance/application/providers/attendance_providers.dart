import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:pos_app/features/attendance/application/notifiers/attendance_notifier.dart';
import 'package:pos_app/features/attendance/infrastructure/repositories/attendance_repository.dart';
import 'package:pos_app/features/auth/application/providers/auth_providers.dart';

// Repository Provider
final attendanceRepositoryProvider = Provider<AttendanceRepository>((ref) {
  return AttendanceRepositoryImpl(
    firestore: FirebaseFirestore.instance,
    networkInfo: ref.watch(networkInfoProvider),
    attendanceBox: Hive.box('attendanceBox'),
  );
});

// Notifier Providers
final attendanceProvider =
    StateNotifierProvider<AttendanceNotifier, AttendanceState>((ref) {
  return AttendanceNotifier(ref.watch(attendanceRepositoryProvider));
});

final attendanceListProvider =
    StateNotifierProvider<AttendanceListNotifier, AttendanceListState>((ref) {
  return AttendanceListNotifier(ref.watch(attendanceRepositoryProvider));
});