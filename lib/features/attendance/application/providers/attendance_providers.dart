import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:pos_app/features/attendance/application/notifiers/attendance_notifier.dart';
import 'package:pos_app/features/attendance/infrastructure/repositories/attendance_repository.dart';
import 'package:pos_app/features/attendance/infrastructure/repositories/attendance_repository_placeholder.dart';
import 'package:pos_app/features/auth/application/providers/auth_providers.dart';
import 'package:pos_app/core/constants/app_constants.dart';

// Repository Provider (Placeholder - will be implemented later)
final attendanceRepositoryProvider = Provider<AttendanceRepository>((ref) {
  // For now, return a placeholder implementation
  // This will be replaced with HTTP-based repository later
  return AttendanceRepositoryPlaceholder(
    networkInfo: ref.watch(networkInfoProvider),
    attendanceBox: Hive.box(AppConstants.attendanceBox),
  );
});

// Notifier Providers
final attendanceProvider =
    StateNotifierProvider<AttendanceNotifier, AttendanceState>((ref) {
  return AttendanceNotifier(ref.watch(attendanceRepositoryProvider));
});

final attendanceListProvider =
    StateNotifierProvider<AttendanceListNotifier, AttendanceListState>((ref) {
  return AttendanceListNotifier(ref.watch(attendanceRepositoryProvider));
});