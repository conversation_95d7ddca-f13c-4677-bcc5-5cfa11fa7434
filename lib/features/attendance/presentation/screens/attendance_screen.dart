import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:pos_app/core/config/app_theme.dart';
import 'package:pos_app/core/widgets/app_button.dart';
import 'package:pos_app/core/widgets/app_error.dart';
import 'package:pos_app/core/widgets/app_loading.dart';
import 'package:pos_app/features/attendance/application/notifiers/attendance_notifier.dart';
import 'package:pos_app/features/attendance/application/providers/attendance_providers.dart';
import 'package:pos_app/features/auth/application/providers/auth_providers.dart';
import 'package:pos_app/features/outlet/application/providers/outlet_providers.dart';
import 'package:pos_app/models/attendance.dart';
import 'package:intl/intl.dart';

class AttendanceScreen extends HookConsumerWidget {
  static const String routeName = '/attendance';

  const AttendanceScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final user = ref.watch(userProvider);
    final outletState = ref.watch(outletStateProvider);
    final attendanceState = ref.watch(attendanceProvider);
    final attendanceListState = ref.watch(attendanceListProvider);
    final isOnline = useState(true);

    // Check internet connection
    useEffect(() {
      final subscription = InternetConnectionChecker.instance.onStatusChange.listen(
        (status) {
          isOnline.value = status == InternetConnectionStatus.connected;
        },
      );
      return subscription.cancel;
    }, const []);

    // Load outlet and attendances on init
    useEffect(() {
      if (user != null) {
        ref.read(outletStateProvider.notifier).getOutlets();
        ref.read(attendanceListProvider.notifier).getAttendances(user.id);
      }
      return null;
    }, const []);

    // Handle attendance state changes
    ref.listen(attendanceProvider, (previous, next) {
      if (next is AttendanceSuccess) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(next.message),
            backgroundColor: AppTheme.successColor,
          ),
        );
        if (user != null) {
          ref.read(attendanceListProvider.notifier).getAttendances(user.id);
        }
      } else if (next is AttendanceError) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(next.message),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    });

    return Scaffold(
      appBar: AppBar(
        title: const Text('Attendance'),
        actions: [
          // Connection status indicator
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(
                  isOnline.value ? Icons.wifi : Icons.wifi_off,
                  color: isOnline.value ? AppTheme.successColor : AppTheme.errorColor,
                ),
                const SizedBox(width: 8),
                Text(
                  isOnline.value ? 'Online' : 'Offline',
                  style: TextStyle(
                    color: isOnline.value ? AppTheme.successColor : AppTheme.errorColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Attendance Actions
          Padding(
            padding: const EdgeInsets.all(16),
            child: outletState.maybeWhen(
              success: (outletSuccess) {
                final outlet = outletSuccess.outlets.firstWhere(
                  (o) => o.id == user?.outletId,
                  orElse: () => throw Exception('Outlet not found'),
                );

                return Row(
                  children: [
                    Expanded(
                      child: AppButton(
                        label: 'Check In',
                        onPressed: () {
                          if (user != null) {
                            ref.read(attendanceProvider.notifier).checkIn(
                                  user.id,
                                  outlet,
                                );
                          }
                        },
                        isLoading: attendanceState is AttendanceLoading,
                        type: AppButtonType.primary,
                        icon: Icons.login,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: AppButton(
                        label: 'Check Out',
                        onPressed: () {
                          if (user != null) {
                            ref.read(attendanceProvider.notifier).checkOut(
                                  user.id,
                                  outlet,
                                );
                          }
                        },
                        isLoading: attendanceState is AttendanceLoading,
                        type: AppButtonType.secondary,
                        icon: Icons.logout,
                      ),
                    ),
                  ],
                );
              },
              orElse: () => const SizedBox.shrink(),
            ),
          ),

          // Attendance History
          Expanded(
            child: Builder(
              builder: (context) {
                if (attendanceListState is AttendanceListLoading) {
                  return const AppLoading();
                }

                if (attendanceListState is AttendanceListError) {
                  return AppError(
                    message: attendanceListState.message,
                    onRetry: () {
                      if (user != null) {
                        ref.read(attendanceListProvider.notifier).getAttendances(
                              user.id,
                            );
                      }
                    },
                  );
                }

                if (attendanceListState is AttendanceListSuccess) {
                  return _AttendanceList(
                    attendances: attendanceListState.attendances,
                  );
                }

                return const SizedBox.shrink();
              },
            ),
          ),
        ],
      ),
    );
  }
}

class _AttendanceList extends StatelessWidget {
  final List<Attendance> attendances;

  const _AttendanceList({required this.attendances});

  @override
  Widget build(BuildContext context) {
    if (attendances.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.history_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No attendance records found',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: attendances.length,
      itemBuilder: (context, index) {
        final attendance = attendances[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          child: ListTile(
            leading: Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: attendance.isComplete
                    ? AppTheme.successColor.withOpacity(0.1)
                    : AppTheme.warningColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                attendance.isComplete
                    ? Icons.check_circle_outline
                    : Icons.pending_outlined,
                color: attendance.isComplete
                    ? AppTheme.successColor
                    : AppTheme.warningColor,
              ),
            ),
            title: Text(
              DateFormat('EEEE, d MMMM y').format(attendance.clockInTime),
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 4),
                Text(
                  'Check In: ${DateFormat('HH:mm').format(attendance.clockInTime)}',
                ),
                if (attendance.clockOutTime != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    'Check Out: ${DateFormat('HH:mm').format(attendance.clockOutTime!)}',
                  ),
                ],
                const SizedBox(height: 4),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: attendance.isSynced
                        ? AppTheme.successColor.withOpacity(0.1)
                        : AppTheme.warningColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    attendance.isSynced ? 'Synced' : 'Not Synced',
                    style: TextStyle(
                      color: attendance.isSynced
                          ? AppTheme.successColor
                          : AppTheme.warningColor,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}