import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:pos_app/core/config/app_theme.dart';
import 'package:pos_app/core/widgets/app_loading.dart';
import 'package:pos_app/features/auth/application/providers/auth_providers.dart';
import 'package:pos_app/features/reports/application/notifiers/reports_notifier.dart';
import 'package:pos_app/features/reports/application/providers/reports_providers.dart';
import 'package:pos_app/models/attendance.dart';
import 'package:pos_app/models/sale.dart';

class ReportsScreen extends HookConsumerWidget {
  static const String routeName = '/reports';

  const ReportsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final user = ref.watch(userProvider);
    final isAdmin = ref.watch(isAdminProvider);
    final reportsState = ref.watch(reportsProvider);
    
    // Date range selection
    final startDate = useState(DateTime.now().subtract(const Duration(days: 7)));
    final endDate = useState(DateTime.now());

    // Load reports on init and date change
    useEffect(() {
      if (user != null) {
        ref.read(reportsProvider.notifier).getReports(
          startDate: startDate.value,
          endDate: endDate.value,
          outletId: isAdmin ? null : user.outletId,
        );
      }
      return null;
    }, [startDate.value, endDate.value]);

    return DefaultTabController(
      length: 2,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Reports'),
          bottom: const TabBar(
            tabs: [
              Tab(text: 'Sales'),
              Tab(text: 'Attendance'),
            ],
          ),
        ),
        body: Column(
          children: [
            // Date Range Selector
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      readOnly: true,
                      decoration: const InputDecoration(
                        labelText: 'Start Date',
                        suffixIcon: Icon(Icons.calendar_today),
                      ),
                      controller: TextEditingController(
                        text: DateFormat('d MMM y').format(startDate.value),
                      ),
                      onTap: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate: startDate.value,
                          firstDate: DateTime(2020),
                          lastDate: DateTime.now(),
                        );
                        if (date != null) {
                          startDate.value = date;
                        }
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextFormField(
                      readOnly: true,
                      decoration: const InputDecoration(
                        labelText: 'End Date',
                        suffixIcon: Icon(Icons.calendar_today),
                      ),
                      controller: TextEditingController(
                        text: DateFormat('d MMM y').format(endDate.value),
                      ),
                      onTap: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate: endDate.value,
                          firstDate: DateTime(2020),
                          lastDate: DateTime.now(),
                        );
                        if (date != null) {
                          endDate.value = date;
                        }
                      },
                    ),
                  ),
                ],
              ),
            ),

            // Reports Content
            Expanded(
              child: TabBarView(
                children: [
                  // Sales Report
                  Builder(
                    builder: (context) {
                      if (reportsState is ReportsLoading) {
                        return const AppLoading();
                      }

                      if (reportsState is ReportsSuccess) {
                        return _SalesReport(
                          sales: reportsState.sales,
                          isAdmin: isAdmin,
                        );
                      }

                      return const SizedBox.shrink();
                    },
                  ),

                  // Attendance Report
                  Builder(
                    builder: (context) {
                      if (reportsState is ReportsLoading) {
                        return const AppLoading();
                      }

                      if (reportsState is ReportsSuccess) {
                        return _AttendanceReport(
                          attendances: reportsState.attendances,
                          isAdmin: isAdmin,
                        );
                      }

                      return const SizedBox.shrink();
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _SalesReport extends StatelessWidget {
  final List<Sale> sales;
  final bool isAdmin;

  const _SalesReport({
    required this.sales,
    required this.isAdmin,
  });

  @override
  Widget build(BuildContext context) {
    if (sales.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.point_of_sale_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No sales data found',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    // Group sales by outlet and date
    final salesByOutlet = <String, Map<String, List<Sale>>>{};
    for (final sale in sales) {
      salesByOutlet[sale.outletId] ??= {};
      final dateKey = DateFormat('yyyy-MM-dd').format(sale.saleDate);
      salesByOutlet[sale.outletId]![dateKey] ??= [];
      salesByOutlet[sale.outletId]![dateKey]!.add(sale);
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: salesByOutlet.length,
      itemBuilder: (context, index) {
        final outletId = salesByOutlet.keys.elementAt(index);
        final salesByDate = salesByOutlet[outletId]!;
        
        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Outlet Header
              Padding(
                padding: const EdgeInsets.all(16),
                child: Text(
                  'Outlet: $outletId',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),
              ),

              // Sales by Date
              ...salesByDate.entries.map((entry) {
                final date = entry.key;
                final salesForDate = entry.value;
                final total = salesForDate.fold<double>(
                  0,
                  (sum, sale) => sum + sale.grandTotal,
                );

                return ListTile(
                  title: Text(
                    DateFormat('EEEE, d MMMM y').format(
                      DateTime.parse(date),
                    ),
                  ),
                  subtitle: Text(
                    '${salesForDate.length} transactions',
                    style: TextStyle(
                      color: Colors.grey[600],
                    ),
                  ),
                  trailing: Text(
                    'Rp ${NumberFormat('#,###').format(total)}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                );
              }).toList(),
            ],
          ),
        );
      },
    );
  }
}

class _AttendanceReport extends StatelessWidget {
  final List<Attendance> attendances;
  final bool isAdmin;

  const _AttendanceReport({
    required this.attendances,
    required this.isAdmin,
  });

  @override
  Widget build(BuildContext context) {
    if (attendances.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.people_outline,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No attendance data found',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    // Group attendances by outlet and employee
    final attendancesByOutlet = <String, Map<String, List<Attendance>>>{};
    for (final attendance in attendances) {
      attendancesByOutlet[attendance.outletId] ??= {};
      attendancesByOutlet[attendance.outletId]![attendance.employeeId] ??= [];
      attendancesByOutlet[attendance.outletId]![attendance.employeeId]!.add(attendance);
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: attendancesByOutlet.length,
      itemBuilder: (context, index) {
        final outletId = attendancesByOutlet.keys.elementAt(index);
        final attendancesByEmployee = attendancesByOutlet[outletId]!;
        
        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Outlet Header
              Padding(
                padding: const EdgeInsets.all(16),
                child: Text(
                  'Outlet: $outletId',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),
              ),

              // Attendance by Employee
              ...attendancesByEmployee.entries.map((entry) {
                final employeeId = entry.key;
                final attendancesForEmployee = entry.value;

                return ExpansionTile(
                  title: Text('Employee: $employeeId'),
                  subtitle: Text(
                    '${attendancesForEmployee.length} records',
                    style: TextStyle(
                      color: Colors.grey[600],
                    ),
                  ),
                  children: attendancesForEmployee.map((attendance) {
                    return ListTile(
                      title: Text(
                        DateFormat('EEEE, d MMMM y').format(
                          attendance.clockInTime,
                        ),
                      ),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Check In: ${DateFormat('HH:mm').format(attendance.clockInTime)}',
                          ),
                          if (attendance.clockOutTime != null)
                            Text(
                              'Check Out: ${DateFormat('HH:mm').format(attendance.clockOutTime!)}',
                            ),
                        ],
                      ),
                      trailing: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: attendance.isComplete
                              ? AppTheme.successColor.withOpacity(0.1)
                              : AppTheme.warningColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          attendance.isComplete ? 'Complete' : 'Incomplete',
                          style: TextStyle(
                            color: attendance.isComplete
                                ? AppTheme.successColor
                                : AppTheme.warningColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                );
              }).toList(),
            ],
          ),
        );
      },
    );
  }
}