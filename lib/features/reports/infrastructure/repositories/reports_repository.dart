import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dartz/dartz.dart';
import 'package:pos_app/core/constants/app_constants.dart';
import 'package:pos_app/core/errors/failures.dart';
import 'package:pos_app/core/utils/logger.dart';
import 'package:pos_app/core/utils/network_info.dart';
import 'package:pos_app/models/attendance.dart';
import 'package:pos_app/models/sale.dart';

abstract class ReportsRepository {
  Future<Either<Failure, (List<Sale>, List<Attendance>)>> getReports({
    required DateTime startDate,
    required DateTime endDate,
    String? outletId,
  });
}

class ReportsRepositoryImpl implements ReportsRepository {
  final FirebaseFirestore _firestore;
  final NetworkInfo _networkInfo;

  ReportsRepositoryImpl({
    required FirebaseFirestore firestore,
    required NetworkInfo networkInfo,
  })  : _firestore = firestore,
        _networkInfo = networkInfo;

  @override
  Future<Either<Failure, (List<Sale>, List<Attendance>)>> getReports({
    required DateTime startDate,
    required DateTime endDate,
    String? outletId,
  }) async {
    try {
      final isConnected = await _networkInfo.isConnected;
      if (!isConnected) {
        return const Left(NetworkFailure(AppConstants.networkErrorMessage));
      }

      // Get sales
      Query salesQuery = _firestore
          .collection(AppConstants.salesCollection)
          .where('saleDate', isGreaterThanOrEqualTo: startDate)
          .where('saleDate', isLessThanOrEqualTo: endDate);

      if (outletId != null) {
        salesQuery = salesQuery.where('outletId', isEqualTo: outletId);
      }

      final salesSnapshot = await salesQuery.get();
      final sales = salesSnapshot.docs
          .map((doc) => Sale.fromMap(doc.data() as Map<String, dynamic>))
          .toList();

      // Get attendances
      Query attendanceQuery = _firestore
          .collection(AppConstants.attendanceCollection)
          .where('clockInTime', isGreaterThanOrEqualTo: startDate)
          .where('clockInTime', isLessThanOrEqualTo: endDate);

      if (outletId != null) {
        attendanceQuery = attendanceQuery.where('outletId', isEqualTo: outletId);
      }

      final attendanceSnapshot = await attendanceQuery.get();
      final attendances = attendanceSnapshot.docs
          .map((doc) => Attendance.fromMap(doc.data() as Map<String, dynamic>))
          .toList();

      return Right((sales, attendances));
    } catch (e) {
      AppLogger.error('Get reports error', e);
      return Left(UnknownFailure(e.toString()));
    }
  }
}