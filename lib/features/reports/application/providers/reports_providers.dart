import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:pos_app/features/auth/application/providers/auth_providers.dart';
import 'package:pos_app/features/reports/application/notifiers/reports_notifier.dart';
import 'package:pos_app/features/reports/infrastructure/repositories/reports_repository.dart';

// Repository Provider
final reportsRepositoryProvider = Provider<ReportsRepository>((ref) {
  return ReportsRepositoryImpl(
    firestore: FirebaseFirestore.instance,
    networkInfo: ref.watch(networkInfoProvider),
  );
});

// Reports Provider
final reportsProvider = StateNotifierProvider<ReportsNotifier, ReportsState>((ref) {
  return ReportsNotifier(ref.watch(reportsRepositoryProvider));
});