import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:pos_app/features/auth/application/providers/auth_providers.dart';
import 'package:pos_app/features/reports/application/notifiers/reports_notifier.dart';
import 'package:pos_app/features/reports/infrastructure/repositories/reports_repository.dart';

// Repository Provider (Placeholder - will be implemented later)
final reportsRepositoryProvider = Provider<ReportsRepository>((ref) {
  // TODO: Implement HTTP-based reports repository
  throw UnimplementedError('Reports repository not yet migrated to HTTP');
});

// Reports Provider
final reportsProvider = StateNotifierProvider<ReportsNotifier, ReportsState>((ref) {
  return ReportsNotifier(ref.watch(reportsRepositoryProvider));
});