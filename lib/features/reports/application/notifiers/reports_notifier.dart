import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:pos_app/core/utils/logger.dart';
import 'package:pos_app/features/reports/infrastructure/repositories/reports_repository.dart';
import 'package:pos_app/models/attendance.dart';
import 'package:pos_app/models/sale.dart';

// Reports State
abstract class ReportsState {
  const ReportsState();
}

class ReportsInitial extends ReportsState {
  const ReportsInitial();
}

class ReportsLoading extends ReportsState {
  const ReportsLoading();
}

class ReportsSuccess extends ReportsState {
  final List<Sale> sales;
  final List<Attendance> attendances;

  const ReportsSuccess({
    required this.sales,
    required this.attendances,
  });
}

class ReportsError extends ReportsState {
  final String message;
  const ReportsError(this.message);
}

// Reports Notifier
class ReportsNotifier extends StateNotifier<ReportsState> {
  final ReportsRepository _repository;

  ReportsNotifier(this._repository) : super(const ReportsInitial());

  Future<void> getReports({
    required DateTime startDate,
    required DateTime endDate,
    String? outletId,
  }) async {
    state = const ReportsLoading();

    final result = await _repository.getReports(
      startDate: startDate,
      endDate: endDate,
      outletId: outletId,
    );

    result.fold(
      (failure) {
        AppLogger.error('Get reports failed: ${failure.message}');
        state = ReportsError(failure.message);
      },
      (success) {
        final sales = success.$1;
        final attendances = success.$2;
        AppLogger.info(
          'Get reports success: ${sales.length} sales, ${attendances.length} attendances',
        );
        state = ReportsSuccess(
          sales: sales,
          attendances: attendances,
        );
      },
    );
  }
}