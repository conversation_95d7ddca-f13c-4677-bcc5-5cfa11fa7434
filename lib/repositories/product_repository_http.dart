import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../core/config/api_config.dart';
import '../core/constants/app_constants.dart';
import '../core/errors/failures.dart';
import '../core/services/http_client_service.dart';
import '../core/utils/logger.dart';
import '../core/utils/network_info.dart';
import '../models/product.dart';

abstract class ProductRepository {
  Future<Either<Failure, List<Product>>> getProducts({
    String? search,
    String? category,
    int page = 1,
    int limit = 20,
  });
  Future<Either<Failure, Product>> getProductById(String id);
  Future<Either<Failure, Product>> getProductByBarcode(String barcode);
  Future<Either<Failure, List<String>>> getCategories();
  Future<Either<Failure, Map<String, dynamic>>> getProductStats();
  Future<Either<Failure, List<Product>>> getLowStockProducts({int threshold = 10});
}

class ProductRepositoryHttp implements ProductRepository {
  final HttpClientService _httpClient;
  final NetworkInfo _networkInfo;

  ProductRepositoryHttp({
    required HttpClientService httpClient,
    required NetworkInfo networkInfo,
  })  : _httpClient = httpClient,
        _networkInfo = networkInfo;

  @override
  Future<Either<Failure, List<Product>>> getProducts({
    String? search,
    String? category,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final isConnected = await _networkInfo.isConnected;
      if (!isConnected) {
        return const Left(NetworkFailure(AppConstants.networkErrorMessage));
      }

      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
        'isActive': true, // Only get active products
      };

      if (search != null && search.isNotEmpty) {
        queryParams['search'] = search;
      }
      if (category != null && category.isNotEmpty) {
        queryParams['category'] = category;
      }

      final response = await _httpClient.get(
        ApiConfig.products,
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        final data = response.data['data'];
        final productsList = (data['data'] as List)
            .map((productData) => Product.fromMap(productData))
            .toList();

        AppLogger.info('Fetched ${productsList.length} products from server');
        return Right(productsList);
      } else {
        return Left(ServerFailure('Failed to fetch products: ${response.statusMessage}'));
      }
    } catch (e) {
      AppLogger.error('Get products error', e);
      return Left(ServerFailure('Failed to fetch products: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, Product>> getProductById(String id) async {
    try {
      final isConnected = await _networkInfo.isConnected;
      if (!isConnected) {
        return const Left(NetworkFailure(AppConstants.networkErrorMessage));
      }

      final response = await _httpClient.get('${ApiConfig.products}/$id');

      if (response.statusCode == 200) {
        final product = Product.fromMap(response.data['data']);
        return Right(product);
      } else if (response.statusCode == 404) {
        return Left(NotFoundFailure('Product not found'));
      } else {
        return Left(ServerFailure('Failed to fetch product: ${response.statusMessage}'));
      }
    } catch (e) {
      AppLogger.error('Get product by ID error', e);
      return Left(ServerFailure('Failed to fetch product: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, Product>> getProductByBarcode(String barcode) async {
    try {
      final isConnected = await _networkInfo.isConnected;
      if (!isConnected) {
        return const Left(NetworkFailure(AppConstants.networkErrorMessage));
      }

      final response = await _httpClient.get('${ApiConfig.products}/barcode/$barcode');

      if (response.statusCode == 200) {
        final product = Product.fromMap(response.data['data']);
        AppLogger.info('Found product by barcode: ${product.name}');
        return Right(product);
      } else if (response.statusCode == 404) {
        return Left(NotFoundFailure('Product with this barcode not found'));
      } else {
        return Left(ServerFailure('Failed to fetch product: ${response.statusMessage}'));
      }
    } catch (e) {
      AppLogger.error('Get product by barcode error', e);
      return Left(ServerFailure('Failed to fetch product: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, List<String>>> getCategories() async {
    try {
      final isConnected = await _networkInfo.isConnected;
      if (!isConnected) {
        return const Left(NetworkFailure(AppConstants.networkErrorMessage));
      }

      final response = await _httpClient.get(ApiConfig.productsCategories);

      if (response.statusCode == 200) {
        final categories = (response.data['data'] as List)
            .map((category) => category.toString())
            .toList();

        return Right(categories);
      } else {
        return Left(ServerFailure('Failed to fetch categories: ${response.statusMessage}'));
      }
    } catch (e) {
      AppLogger.error('Get categories error', e);
      return Left(ServerFailure('Failed to fetch categories: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getProductStats() async {
    try {
      final isConnected = await _networkInfo.isConnected;
      if (!isConnected) {
        return const Left(NetworkFailure(AppConstants.networkErrorMessage));
      }

      final response = await _httpClient.get(ApiConfig.productsStats);

      if (response.statusCode == 200) {
        final stats = response.data['data'] as Map<String, dynamic>;
        return Right(stats);
      } else {
        return Left(ServerFailure('Failed to fetch product stats: ${response.statusMessage}'));
      }
    } catch (e) {
      AppLogger.error('Get product stats error', e);
      return Left(ServerFailure('Failed to fetch product stats: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, List<Product>>> getLowStockProducts({int threshold = 10}) async {
    try {
      final isConnected = await _networkInfo.isConnected;
      if (!isConnected) {
        return const Left(NetworkFailure(AppConstants.networkErrorMessage));
      }

      final response = await _httpClient.get(
        ApiConfig.productsLowStock,
        queryParameters: {'threshold': threshold},
      );

      if (response.statusCode == 200) {
        final productsList = (response.data['data'] as List)
            .map((productData) => Product.fromMap(productData))
            .toList();

        AppLogger.info('Found ${productsList.length} low stock products');
        return Right(productsList);
      } else {
        return Left(ServerFailure('Failed to fetch low stock products: ${response.statusMessage}'));
      }
    } catch (e) {
      AppLogger.error('Get low stock products error', e);
      return Left(ServerFailure('Failed to fetch low stock products: ${e.toString()}'));
    }
  }

  // Additional methods for product management (Admin only)
  Future<Either<Failure, Product>> createProduct(Product product) async {
    try {
      final isConnected = await _networkInfo.isConnected;
      if (!isConnected) {
        return const Left(NetworkFailure(AppConstants.networkErrorMessage));
      }

      final response = await _httpClient.post(
        ApiConfig.products,
        data: product.toMap(),
      );

      if (response.statusCode == 201) {
        final newProduct = Product.fromMap(response.data['data']);
        AppLogger.info('Product created: ${newProduct.name}');
        return Right(newProduct);
      } else {
        return Left(ServerFailure('Failed to create product: ${response.statusMessage}'));
      }
    } catch (e) {
      AppLogger.error('Create product error', e);
      return Left(ServerFailure('Failed to create product: ${e.toString()}'));
    }
  }

  Future<Either<Failure, Product>> updateProduct(Product product) async {
    try {
      final isConnected = await _networkInfo.isConnected;
      if (!isConnected) {
        return const Left(NetworkFailure(AppConstants.networkErrorMessage));
      }

      final response = await _httpClient.patch(
        '${ApiConfig.products}/${product.id}',
        data: product.toMap(),
      );

      if (response.statusCode == 200) {
        final updatedProduct = Product.fromMap(response.data['data']);
        AppLogger.info('Product updated: ${updatedProduct.name}');
        return Right(updatedProduct);
      } else if (response.statusCode == 404) {
        return Left(NotFoundFailure('Product not found'));
      } else {
        return Left(ServerFailure('Failed to update product: ${response.statusMessage}'));
      }
    } catch (e) {
      AppLogger.error('Update product error', e);
      return Left(ServerFailure('Failed to update product: ${e.toString()}'));
    }
  }

  Future<Either<Failure, void>> deleteProduct(String productId) async {
    try {
      final isConnected = await _networkInfo.isConnected;
      if (!isConnected) {
        return const Left(NetworkFailure(AppConstants.networkErrorMessage));
      }

      final response = await _httpClient.delete('${ApiConfig.products}/$productId');

      if (response.statusCode == 204) {
        AppLogger.info('Product deleted: $productId');
        return const Right(null);
      } else if (response.statusCode == 404) {
        return Left(NotFoundFailure('Product not found'));
      } else {
        return Left(ServerFailure('Failed to delete product: ${response.statusMessage}'));
      }
    } catch (e) {
      AppLogger.error('Delete product error', e);
      return Left(ServerFailure('Failed to delete product: ${e.toString()}'));
    }
  }

  Future<Either<Failure, Product>> updateProductStock(String productId, int quantity) async {
    try {
      final isConnected = await _networkInfo.isConnected;
      if (!isConnected) {
        return const Left(NetworkFailure(AppConstants.networkErrorMessage));
      }

      final response = await _httpClient.patch(
        '${ApiConfig.products}/$productId/stock',
        data: {'quantity': quantity},
      );

      if (response.statusCode == 200) {
        final updatedProduct = Product.fromMap(response.data['data']);
        AppLogger.info('Product stock updated: ${updatedProduct.name}');
        return Right(updatedProduct);
      } else if (response.statusCode == 404) {
        return const Left(NotFoundFailure('Product not found'));
      } else if (response.statusCode == 400) {
        return Left(ValidationFailure('Insufficient stock'));
      } else {
        return Left(ServerFailure('Failed to update stock: ${response.statusMessage}'));
      }
    } catch (e) {
      AppLogger.error('Update product stock error', e);
      return Left(ServerFailure('Failed to update stock: ${e.toString()}'));
    }
  }
}

// Provider for ProductRepository
final productRepositoryProvider = Provider<ProductRepository>((ref) {
  final httpClient = ref.watch(httpClientServiceProvider);
  final networkInfo = ref.watch(networkInfoProvider);

  return ProductRepositoryHttp(
    httpClient: httpClient,
    networkInfo: networkInfo,
  );
});
