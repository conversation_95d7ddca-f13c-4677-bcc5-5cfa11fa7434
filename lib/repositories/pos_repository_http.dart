import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive/hive.dart';

import '../core/config/api_config.dart';
import '../core/constants/app_constants.dart';
import '../core/errors/failures.dart';
import '../core/services/http_client_service.dart';
import '../core/utils/logger.dart';
import '../core/utils/network_info.dart';
import '../models/sale.dart';

abstract class POSRepository {
  Future<Either<Failure, Sale>> createSale(Sale sale);
  Future<Either<Failure, List<Sale>>> getSales({
    String? outletId,
    DateTime? startDate,
    DateTime? endDate,
    int page = 1,
    int limit = 20,
  });
  Future<Either<Failure, void>> syncOfflineSales();
  Future<int> getOfflineSalesCount();
}

class POSRepositoryHttp implements POSRepository {
  final HttpClientService _httpClient;
  final NetworkInfo _networkInfo;
  final Box _saleBox;

  POSRepositoryHttp({
    required HttpClientService httpClient,
    required NetworkInfo networkInfo,
    required Box saleBox,
  })  : _httpClient = httpClient,
        _networkInfo = networkInfo,
        _saleBox = saleBox;

  @override
  Future<Either<Failure, Sale>> createSale(Sale sale) async {
    try {
      final isConnected = await _networkInfo.isConnected;

      if (!isConnected) {
        // Save offline with temporary ID
        final offlineSale = sale.copyWith(
          id: 'offline_${DateTime.now().millisecondsSinceEpoch}',
          isSynced: false,
        );
        
        await _saleBox.add({
          ...offlineSale.toMap(),
          '_offline_key': _saleBox.length,
        });
        
        AppLogger.info('Sale saved offline: ${offlineSale.id}');
        return Right(offlineSale);
      }

      // Save online
      final response = await _httpClient.post(
        ApiConfig.sales,
        data: sale.toMap(),
      );

      if (response.statusCode == 201) {
        final newSale = Sale.fromMap(response.data['data']);
        AppLogger.info('Sale created online: ${newSale.id}');
        return Right(newSale);
      } else {
        return Left(ServerFailure('Failed to create sale: ${response.statusMessage}'));
      }
    } catch (e) {
      AppLogger.error('Create sale error', e);
      
      // Check if it's a network error
      if (e.toString().contains('network') || 
          e.toString().contains('connection') ||
          e.toString().contains('timeout')) {
        // Network error, save offline
        final offlineSale = sale.copyWith(
          id: 'offline_${DateTime.now().millisecondsSinceEpoch}',
          isSynced: false,
        );
        
        await _saleBox.add({
          ...offlineSale.toMap(),
          '_offline_key': _saleBox.length,
        });
        
        AppLogger.info('Sale saved offline due to network error: ${offlineSale.id}');
        return Right(offlineSale);
      }
      
      return Left(ServerFailure('Failed to create sale: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, List<Sale>>> getSales({
    String? outletId,
    DateTime? startDate,
    DateTime? endDate,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final isConnected = await _networkInfo.isConnected;
      if (!isConnected) {
        return const Left(NetworkFailure(AppConstants.networkErrorMessage));
      }

      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };

      if (outletId != null) queryParams['outletId'] = outletId;
      if (startDate != null) {
        queryParams['startDate'] = startDate.toIso8601String();
      }
      if (endDate != null) {
        queryParams['endDate'] = endDate.toIso8601String();
      }

      final response = await _httpClient.get(
        ApiConfig.sales,
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        final data = response.data['data'];
        final salesList = (data['data'] as List)
            .map((saleData) => Sale.fromMap(saleData))
            .toList();

        AppLogger.info('Fetched ${salesList.length} sales from server');
        return Right(salesList);
      } else {
        return Left(ServerFailure('Failed to fetch sales: ${response.statusMessage}'));
      }
    } catch (e) {
      AppLogger.error('Get sales error', e);
      return Left(ServerFailure('Failed to fetch sales: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, void>> syncOfflineSales() async {
    try {
      final isConnected = await _networkInfo.isConnected;
      if (!isConnected) {
        return const Left(NetworkFailure(AppConstants.networkErrorMessage));
      }

      final offlineData = _saleBox.values.toList();
      final offlineSales = offlineData
          .map((data) {
            final saleMap = Map<String, dynamic>.from(data);
            return {
              'sale': Sale.fromMap(saleMap),
              'key': saleMap['_offline_key'],
            };
          })
          .where((item) => !item['sale'].isSynced)
          .toList();

      if (offlineSales.isEmpty) {
        AppLogger.info('No offline sales to sync');
        return const Right(null);
      }

      AppLogger.info('Syncing ${offlineSales.length} offline sales...');

      // Batch sync for better performance
      final batchData = offlineSales.map((item) => item['sale'].toMap()).toList();
      
      final response = await _httpClient.post(
        ApiConfig.salesBatch,
        data: {'sales': batchData},
      );

      if (response.statusCode == 201) {
        final responseData = response.data['data'];
        final successCount = responseData['success'] ?? 0;
        final failedCount = responseData['failed'] ?? 0;

        // Remove successfully synced sales from offline storage
        if (successCount > 0) {
          for (int i = 0; i < successCount && i < offlineSales.length; i++) {
            final offlineKey = offlineSales[i]['key'];
            await _saleBox.delete(offlineKey);
          }
        }

        AppLogger.info('Sync completed: $successCount success, $failedCount failed');
        
        if (failedCount > 0) {
          return Left(ServerFailure('Some sales failed to sync: $failedCount'));
        }
        
        return const Right(null);
      } else {
        return Left(ServerFailure('Batch sync failed: ${response.statusMessage}'));
      }
    } catch (e) {
      AppLogger.error('Sync offline sales error', e);
      return Left(ServerFailure('Sync failed: ${e.toString()}'));
    }
  }

  @override
  Future<int> getOfflineSalesCount() async {
    try {
      final offlineData = _saleBox.values.toList();
      final offlineSales = offlineData
          .map((data) => Sale.fromMap(Map<String, dynamic>.from(data)))
          .where((sale) => !sale.isSynced)
          .length;
      
      return offlineSales;
    } catch (e) {
      AppLogger.error('Get offline sales count error', e);
      return 0;
    }
  }

  // Helper method to clear all offline data (for testing)
  Future<void> clearOfflineData() async {
    try {
      await _saleBox.clear();
      AppLogger.info('Offline sales data cleared');
    } catch (e) {
      AppLogger.error('Clear offline data error', e);
    }
  }

  // Get sales reports
  Future<Either<Failure, Map<String, dynamic>>> getDailySalesReport({
    required DateTime date,
    String? outletId,
  }) async {
    try {
      final isConnected = await _networkInfo.isConnected;
      if (!isConnected) {
        return const Left(NetworkFailure(AppConstants.networkErrorMessage));
      }

      final queryParams = <String, dynamic>{
        'date': date.toIso8601String().split('T')[0], // YYYY-MM-DD format
      };

      if (outletId != null) queryParams['outletId'] = outletId;

      final response = await _httpClient.get(
        ApiConfig.salesDaily,
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        return Right(response.data['data']);
      } else {
        return Left(ServerFailure('Failed to fetch daily report: ${response.statusMessage}'));
      }
    } catch (e) {
      AppLogger.error('Get daily sales report error', e);
      return Left(ServerFailure('Failed to fetch daily report: ${e.toString()}'));
    }
  }

  // Get sales summary
  Future<Either<Failure, List<Map<String, dynamic>>>> getSalesSummary({
    required DateTime startDate,
    required DateTime endDate,
    String? outletId,
  }) async {
    try {
      final isConnected = await _networkInfo.isConnected;
      if (!isConnected) {
        return const Left(NetworkFailure(AppConstants.networkErrorMessage));
      }

      final queryParams = <String, dynamic>{
        'startDate': startDate.toIso8601String().split('T')[0],
        'endDate': endDate.toIso8601String().split('T')[0],
      };

      if (outletId != null) queryParams['outletId'] = outletId;

      final response = await _httpClient.get(
        ApiConfig.salesSummary,
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        final data = response.data['data'] as List;
        return Right(data.cast<Map<String, dynamic>>());
      } else {
        return Left(ServerFailure('Failed to fetch sales summary: ${response.statusMessage}'));
      }
    } catch (e) {
      AppLogger.error('Get sales summary error', e);
      return Left(ServerFailure('Failed to fetch sales summary: ${e.toString()}'));
    }
  }
}

// Provider for POSRepository
final posRepositoryProvider = Provider<POSRepository>((ref) {
  final httpClient = ref.watch(httpClientServiceProvider);
  final networkInfo = ref.watch(networkInfoProvider);
  final saleBox = ref.watch(saleBoxProvider);
  
  return POSRepositoryHttp(
    httpClient: httpClient,
    networkInfo: networkInfo,
    saleBox: saleBox,
  );
});

// Provider for sale box (Hive)
final saleBoxProvider = Provider<Box>((ref) {
  throw UnimplementedError('Sale box must be overridden');
});
