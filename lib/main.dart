import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:pos_app/core/config/app_theme.dart';
import 'package:pos_app/core/constants/app_constants.dart';
import 'package:pos_app/core/routes/app_router.dart';
import 'package:pos_app/features/auth/presentation/screens/splash_screen.dart';
import 'package:pos_app/features/auth/application/providers/auth_providers.dart';
import 'package:pos_app/repositories/pos_repository_http.dart';
import 'package:pos_app/models/adapters/attendance_adapter.dart';
import 'package:pos_app/models/adapters/sale_adapter.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // Initialize SharedPreferences
  final sharedPreferences = await SharedPreferences.getInstance();

  // Initialize Hive for local storage
  await Hive.initFlutter();

  // Register Hive adapters
  Hive.registerAdapter(SaleAdapter());
  Hive.registerAdapter(SaleItemAdapter());
  Hive.registerAdapter(PaymentMethodAdapter());
  Hive.registerAdapter(AttendanceAdapter());

  // Open Hive boxes
  final saleBox = await Hive.openBox(AppConstants.saleBox);
  final attendanceBox = await Hive.openBox(AppConstants.attendanceBox);

  runApp(
    ProviderScope(
      overrides: [
        // Override SharedPreferences provider
        sharedPreferencesProvider.overrideWithValue(sharedPreferences),
        // Override Hive boxes
        saleBoxProvider.overrideWithValue(saleBox),
        // Note: attendanceBox can be added later when needed
      ],
      child: const MyApp(),
    ),
  );
}

class MyApp extends ConsumerWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return MaterialApp(
      title: 'POS & Outlet Management',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system,
      onGenerateRoute: AppRouter.onGenerateRoute,
      home: const SplashScreen(),
    );
  }
}