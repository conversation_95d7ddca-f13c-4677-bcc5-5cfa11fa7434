import 'package:equatable/equatable.dart';
import 'package:uuid/uuid.dart';

enum PaymentMethod {
  cash,
  card,
  digitalWallet, // Updated to match backend enum
  bankTransfer,  // Added new payment method
}

class SaleItem extends Equatable {
  final String productId;
  final String productName;
  final double price;
  final int quantity;
  final double total;

  const SaleItem({
    required this.productId,
    required this.productName,
    required this.price,
    required this.quantity,
    required this.total,
  });

  factory SaleItem.empty() => const SaleItem(
        productId: '',
        productName: '',
        price: 0.0,
        quantity: 0,
        total: 0.0,
      );

  SaleItem copyWith({
    String? productId,
    String? productName,
    double? price,
    int? quantity,
    double? total,
  }) {
    return SaleItem(
      productId: productId ?? this.productId,
      productName: productName ?? this.productName,
      price: price ?? this.price,
      quantity: quantity ?? this.quantity,
      total: total ?? this.total,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'productId': productId,
      'productName': productName,
      'price': price,
      'quantity': quantity,
      'total': total,
    };
  }

  factory SaleItem.fromMap(Map<String, dynamic> map) {
    return SaleItem(
      productId: map['productId'] as String,
      productName: map['productName'] as String,
      price: (map['price'] as num).toDouble(),
      quantity: map['quantity'] as int,
      total: (map['total'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson() => toMap();

  factory SaleItem.fromJson(Map<String, dynamic> json) => SaleItem.fromMap(json);

  @override
  List<Object?> get props => [
        productId,
        productName,
        price,
        quantity,
        total,
      ];
}

class Sale extends Equatable {
  final String id;
  final String outletId;
  final String employeeId;
  final List<SaleItem> items;
  final double subtotal;
  final double tax;
  final double discount;
  final double grandTotal;
  final PaymentMethod paymentMethod;
  final DateTime saleDate;
  final bool isSynced;
  final DateTime createdAt;

  const Sale({
    required this.id,
    required this.outletId,
    required this.employeeId,
    required this.items,
    required this.subtotal,
    required this.tax,
    required this.discount,
    required this.grandTotal,
    required this.paymentMethod,
    required this.saleDate,
    this.isSynced = false,
    required this.createdAt,
  });

  factory Sale.empty() {
    final now = DateTime.now();
    return Sale(
      id: const Uuid().v4(),
      outletId: '',
      employeeId: '',
      items: const [],
      subtotal: 0.0,
      tax: 0.0,
      discount: 0.0,
      grandTotal: 0.0,
      paymentMethod: PaymentMethod.cash,
      saleDate: now,
      isSynced: false,
      createdAt: now,
    );
  }

  Sale copyWith({
    String? id,
    String? outletId,
    String? employeeId,
    List<SaleItem>? items,
    double? subtotal,
    double? tax,
    double? discount,
    double? grandTotal,
    PaymentMethod? paymentMethod,
    DateTime? saleDate,
    bool? isSynced,
    DateTime? createdAt,
  }) {
    return Sale(
      id: id ?? this.id,
      outletId: outletId ?? this.outletId,
      employeeId: employeeId ?? this.employeeId,
      items: items ?? this.items,
      subtotal: subtotal ?? this.subtotal,
      tax: tax ?? this.tax,
      discount: discount ?? this.discount,
      grandTotal: grandTotal ?? this.grandTotal,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      saleDate: saleDate ?? this.saleDate,
      isSynced: isSynced ?? this.isSynced,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'outletId': outletId,
      'employeeId': employeeId,
      'items': items.map((x) => x.toMap()).toList(),
      'subtotal': subtotal,
      'tax': tax,
      'discount': discount,
      'grandTotal': grandTotal,
      'paymentMethod': _paymentMethodToString(paymentMethod),
      'saleDate': saleDate.toIso8601String(),
      'isSynced': isSynced,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory Sale.fromMap(Map<String, dynamic> map) {
    // Handle both Firebase and HTTP API formats
    DateTime parseDate(dynamic dateValue) {
      if (dateValue is String) {
        return DateTime.parse(dateValue);
      } else if (dateValue != null && dateValue.runtimeType.toString().contains('Timestamp')) {
        // Firebase Timestamp
        return (dateValue as dynamic).toDate();
      } else {
        return DateTime.now();
      }
    }

    return Sale(
      id: map['id'] as String,
      outletId: map['outletId'] as String,
      employeeId: map['employeeId'] as String,
      items: List<SaleItem>.from(
        (map['items'] as List).map(
          (x) => SaleItem.fromMap(x as Map<String, dynamic>),
        ),
      ),
      subtotal: (map['subtotal'] as num).toDouble(),
      tax: (map['tax'] as num).toDouble(),
      discount: (map['discount'] as num).toDouble(),
      grandTotal: (map['grandTotal'] as num).toDouble(),
      paymentMethod: _paymentMethodFromString(map['paymentMethod'] as String),
      saleDate: parseDate(map['saleDate']),
      isSynced: map['isSynced'] as bool? ?? false,
      createdAt: parseDate(map['createdAt']),
    );
  }

  static PaymentMethod _paymentMethodFromString(String method) {
    switch (method.toLowerCase()) {
      case 'cash':
        return PaymentMethod.cash;
      case 'card':
        return PaymentMethod.card;
      case 'digital_wallet':
      case 'digitalwallet':
        return PaymentMethod.digitalWallet;
      case 'bank_transfer':
      case 'banktransfer':
        return PaymentMethod.bankTransfer;
      default:
        return PaymentMethod.cash;
    }
  }

  static String _paymentMethodToString(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.cash:
        return 'cash';
      case PaymentMethod.card:
        return 'card';
      case PaymentMethod.digitalWallet:
        return 'digital_wallet';
      case PaymentMethod.bankTransfer:
        return 'bank_transfer';
    }
  }

  Map<String, dynamic> toJson() => toMap();

  factory Sale.fromJson(Map<String, dynamic> json) => Sale.fromMap(json);

  @override
  List<Object?> get props => [
        id,
        outletId,
        employeeId,
        items,
        subtotal,
        tax,
        discount,
        grandTotal,
        paymentMethod,
        saleDate,
        isSynced,
        createdAt,
      ];
}