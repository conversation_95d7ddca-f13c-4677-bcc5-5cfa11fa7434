import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';

class Outlet extends Equatable {
  final String id;
  final String name;
  final String address;
  final GeoPoint location;
  final String phone;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Outlet({
    required this.id,
    required this.name,
    required this.address,
    required this.location,
    required this.phone,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Outlet.empty() => Outlet(
        id: '',
        name: '',
        address: '',
        location: const GeoPoint(0, 0),
        phone: '',
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

  Outlet copyWith({
    String? id,
    String? name,
    String? address,
    GeoPoint? location,
    String? phone,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Outlet(
      id: id ?? this.id,
      name: name ?? this.name,
      address: address ?? this.address,
      location: location ?? this.location,
      phone: phone ?? this.phone,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'address': address,
      'location': location,
      'phone': phone,
      'isActive': isActive,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  factory Outlet.fromMap(Map<String, dynamic> map) {
    return Outlet(
      id: map['id'] as String,
      name: map['name'] as String,
      address: map['address'] as String,
      location: map['location'] as GeoPoint,
      phone: map['phone'] as String,
      isActive: map['isActive'] as bool? ?? true,
      createdAt: (map['createdAt'] as Timestamp).toDate(),
      updatedAt: (map['updatedAt'] as Timestamp).toDate(),
    );
  }

  Map<String, dynamic> toJson() => toMap();

  factory Outlet.fromJson(Map<String, dynamic> json) => Outlet.fromMap(json);

  @override
  List<Object?> get props => [
        id,
        name,
        address,
        location,
        phone,
        isActive,
        createdAt,
        updatedAt,
      ];
}