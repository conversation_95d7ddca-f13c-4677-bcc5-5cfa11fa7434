import 'package:equatable/equatable.dart';

class OutletLocation extends Equatable {
  final double latitude;
  final double longitude;

  const OutletLocation({
    required this.latitude,
    required this.longitude,
  });

  Map<String, dynamic> toMap() {
    return {
      'latitude': latitude,
      'longitude': longitude,
    };
  }

  factory OutletLocation.fromMap(Map<String, dynamic> map) {
    return OutletLocation(
      latitude: (map['latitude'] as num).toDouble(),
      longitude: (map['longitude'] as num).toDouble(),
    );
  }

  @override
  List<Object?> get props => [latitude, longitude];
}

class Outlet extends Equatable {
  final String id;
  final String name;
  final String address;
  final OutletLocation location;
  final String phone;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Outlet({
    required this.id,
    required this.name,
    required this.address,
    required this.location,
    required this.phone,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Outlet.empty() => Outlet(
        id: '',
        name: '',
        address: '',
        location: const OutletLocation(latitude: 0, longitude: 0),
        phone: '',
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

  Outlet copyWith({
    String? id,
    String? name,
    String? address,
    OutletLocation? location,
    String? phone,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Outlet(
      id: id ?? this.id,
      name: name ?? this.name,
      address: address ?? this.address,
      location: location ?? this.location,
      phone: phone ?? this.phone,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'address': address,
      'location': location.toMap(),
      'phone': phone,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory Outlet.fromMap(Map<String, dynamic> map) {
    // Handle both Firebase and HTTP API formats
    DateTime parseDate(dynamic dateValue) {
      if (dateValue is String) {
        return DateTime.parse(dateValue);
      } else if (dateValue != null && dateValue.runtimeType.toString().contains('Timestamp')) {
        // Firebase Timestamp
        return (dateValue as dynamic).toDate();
      } else {
        return DateTime.now();
      }
    }

    OutletLocation parseLocation(dynamic locationValue) {
      if (locationValue is Map<String, dynamic>) {
        return OutletLocation.fromMap(locationValue);
      } else if (locationValue != null && locationValue.runtimeType.toString().contains('GeoPoint')) {
        // Firebase GeoPoint
        final geoPoint = locationValue as dynamic;
        return OutletLocation(
          latitude: geoPoint.latitude,
          longitude: geoPoint.longitude,
        );
      } else {
        return const OutletLocation(latitude: 0, longitude: 0);
      }
    }

    return Outlet(
      id: map['id'] as String,
      name: map['name'] as String,
      address: map['address'] as String,
      location: parseLocation(map['location']),
      phone: map['phone'] as String,
      isActive: map['isActive'] as bool? ?? true,
      createdAt: parseDate(map['createdAt']),
      updatedAt: parseDate(map['updatedAt']),
    );
  }

  Map<String, dynamic> toJson() => toMap();

  factory Outlet.fromJson(Map<String, dynamic> json) => Outlet.fromMap(json);

  @override
  List<Object?> get props => [
        id,
        name,
        address,
        location,
        phone,
        isActive,
        createdAt,
        updatedAt,
      ];
}