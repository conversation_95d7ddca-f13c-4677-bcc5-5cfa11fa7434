import 'package:equatable/equatable.dart';

class Product extends Equatable {
  final String id;
  final String name;
  final String description;
  final double price;
  final String category;
  final String? imageUrl;
  final Map<String, int> stock; // Map<outletId, stockQuantity> - Firebase format
  final int stockQuantity; // HTTP API format
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Product({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.category,
    this.imageUrl,
    required this.stock,
    this.stockQuantity = 0,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Product.empty() => Product(
        id: '',
        name: '',
        description: '',
        price: 0.0,
        category: '',
        imageUrl: null,
        stock: const {},
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

  Product copyWith({
    String? id,
    String? name,
    String? description,
    double? price,
    String? category,
    String? imageUrl,
    Map<String, int>? stock,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Product(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      price: price ?? this.price,
      category: category ?? this.category,
      imageUrl: imageUrl ?? this.imageUrl,
      stock: stock ?? this.stock,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'price': price,
      'category': category,
      'imageUrl': imageUrl,
      'stock': stock,
      'stockQuantity': stockQuantity,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory Product.fromMap(Map<String, dynamic> map) {
    // Handle both Firebase and HTTP API formats
    DateTime parseDate(dynamic dateValue) {
      if (dateValue is String) {
        return DateTime.parse(dateValue);
      } else if (dateValue != null && dateValue.runtimeType.toString().contains('Timestamp')) {
        // Firebase Timestamp
        return (dateValue as dynamic).toDate();
      } else {
        return DateTime.now();
      }
    }

    // Handle stock - Firebase uses Map, HTTP API uses int
    Map<String, int> stockMap = {};
    int stockQty = 0;

    if (map['stock'] is Map) {
      stockMap = Map<String, int>.from(map['stock'] as Map);
    } else if (map['stockQuantity'] is int) {
      stockQty = map['stockQuantity'] as int;
      // Create a default stock map for backward compatibility
      stockMap = {'default': stockQty};
    }

    return Product(
      id: map['id'] as String,
      name: map['name'] as String,
      description: map['description'] as String? ?? '',
      price: (map['price'] as num).toDouble(),
      category: map['category'] as String? ?? '',
      imageUrl: map['imageUrl'] as String?,
      stock: stockMap,
      stockQuantity: stockQty,
      isActive: map['isActive'] as bool? ?? true,
      createdAt: parseDate(map['createdAt']),
      updatedAt: parseDate(map['updatedAt']),
    );
  }

  Map<String, dynamic> toJson() => toMap();

  factory Product.fromJson(Map<String, dynamic> json) => Product.fromMap(json);

  int getStockForOutlet(String outletId) {
    return stock[outletId] ?? 0;
  }

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        price,
        category,
        imageUrl,
        stock,
        isActive,
        createdAt,
        updatedAt,
      ];
}