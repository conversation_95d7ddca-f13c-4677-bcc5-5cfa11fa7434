import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';
import 'package:uuid/uuid.dart';

class Attendance extends Equatable {
  final String id;
  final String employeeId;
  final String outletId;
  final DateTime clockInTime;
  final GeoPoint clockInLocation;
  final DateTime? clockOutTime;
  final GeoPoint? clockOutLocation;
  final bool isSynced;

  const Attendance({
    required this.id,
    required this.employeeId,
    required this.outletId,
    required this.clockInTime,
    required this.clockInLocation,
    this.clockOutTime,
    this.clockOutLocation,
    this.isSynced = false,
  });

  factory Attendance.empty() => Attendance(
        id: const Uuid().v4(),
        employeeId: '',
        outletId: '',
        clockInTime: DateTime.now(),
        clockInLocation: const GeoPoint(0, 0),
        clockOutTime: null,
        clockOutLocation: null,
        isSynced: false,
      );

  Attendance copyWith({
    String? id,
    String? employeeId,
    String? outletId,
    DateTime? clockInTime,
    GeoPoint? clockInLocation,
    DateTime? clockOutTime,
    GeoPoint? clockOutLocation,
    bool? isSynced,
  }) {
    return Attendance(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      outletId: outletId ?? this.outletId,
      clockInTime: clockInTime ?? this.clockInTime,
      clockInLocation: clockInLocation ?? this.clockInLocation,
      clockOutTime: clockOutTime ?? this.clockOutTime,
      clockOutLocation: clockOutLocation ?? this.clockOutLocation,
      isSynced: isSynced ?? this.isSynced,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'employeeId': employeeId,
      'outletId': outletId,
      'clockInTime': Timestamp.fromDate(clockInTime),
      'clockInLocation': clockInLocation,
      'clockOutTime': clockOutTime != null ? Timestamp.fromDate(clockOutTime!) : null,
      'clockOutLocation': clockOutLocation,
      'isSynced': isSynced,
    };
  }

  factory Attendance.fromMap(Map<String, dynamic> map) {
    return Attendance(
      id: map['id'] as String,
      employeeId: map['employeeId'] as String,
      outletId: map['outletId'] as String,
      clockInTime: (map['clockInTime'] as Timestamp).toDate(),
      clockInLocation: map['clockInLocation'] as GeoPoint,
      clockOutTime: map['clockOutTime'] != null 
          ? (map['clockOutTime'] as Timestamp).toDate() 
          : null,
      clockOutLocation: map['clockOutLocation'] as GeoPoint?,
      isSynced: map['isSynced'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() => toMap();

  factory Attendance.fromJson(Map<String, dynamic> json) => Attendance.fromMap(json);

  bool get isComplete => clockOutTime != null && clockOutLocation != null;

  Duration get duration {
    if (clockOutTime == null) {
      return Duration.zero;
    }
    return clockOutTime!.difference(clockInTime);
  }

  @override
  List<Object?> get props => [
        id,
        employeeId,
        outletId,
        clockInTime,
        clockInLocation,
        clockOutTime,
        clockOutLocation,
        isSynced,
      ];
}