import 'package:equatable/equatable.dart';
import 'package:uuid/uuid.dart';

class AttendanceLocation extends Equatable {
  final double latitude;
  final double longitude;

  const AttendanceLocation({
    required this.latitude,
    required this.longitude,
  });

  Map<String, dynamic> toMap() {
    return {
      'latitude': latitude,
      'longitude': longitude,
    };
  }

  factory AttendanceLocation.fromMap(Map<String, dynamic> map) {
    return AttendanceLocation(
      latitude: (map['latitude'] as num).toDouble(),
      longitude: (map['longitude'] as num).toDouble(),
    );
  }

  @override
  List<Object?> get props => [latitude, longitude];
}

class Attendance extends Equatable {
  final String id;
  final String employeeId;
  final String outletId;
  final DateTime checkInTime;
  final AttendanceLocation checkInLocation;
  final DateTime? checkOutTime;
  final AttendanceLocation? checkOutLocation;
  final bool isSynced;
  final DateTime createdAt;

  const Attendance({
    required this.id,
    required this.employeeId,
    required this.outletId,
    required this.checkInTime,
    required this.checkInLocation,
    this.checkOutTime,
    this.checkOutLocation,
    this.isSynced = false,
    required this.createdAt,
  });

  factory Attendance.empty() => Attendance(
        id: const Uuid().v4(),
        employeeId: '',
        outletId: '',
        checkInTime: DateTime.now(),
        checkInLocation: const AttendanceLocation(latitude: 0, longitude: 0),
        checkOutTime: null,
        checkOutLocation: null,
        isSynced: false,
        createdAt: DateTime.now(),
      );

  Attendance copyWith({
    String? id,
    String? employeeId,
    String? outletId,
    DateTime? checkInTime,
    AttendanceLocation? checkInLocation,
    DateTime? checkOutTime,
    AttendanceLocation? checkOutLocation,
    bool? isSynced,
    DateTime? createdAt,
  }) {
    return Attendance(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      outletId: outletId ?? this.outletId,
      checkInTime: checkInTime ?? this.checkInTime,
      checkInLocation: checkInLocation ?? this.checkInLocation,
      checkOutTime: checkOutTime ?? this.checkOutTime,
      checkOutLocation: checkOutLocation ?? this.checkOutLocation,
      isSynced: isSynced ?? this.isSynced,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'employeeId': employeeId,
      'outletId': outletId,
      'checkInTime': checkInTime.toIso8601String(),
      'checkInLocation': checkInLocation.toMap(),
      'checkOutTime': checkOutTime?.toIso8601String(),
      'checkOutLocation': checkOutLocation?.toMap(),
      'isSynced': isSynced,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory Attendance.fromMap(Map<String, dynamic> map) {
    // Handle both Firebase and HTTP API formats
    DateTime parseDate(dynamic dateValue) {
      if (dateValue is String) {
        return DateTime.parse(dateValue);
      } else if (dateValue != null && dateValue.runtimeType.toString().contains('Timestamp')) {
        // Firebase Timestamp
        return (dateValue as dynamic).toDate();
      } else {
        return DateTime.now();
      }
    }

    AttendanceLocation parseLocation(dynamic locationValue) {
      if (locationValue is Map<String, dynamic>) {
        return AttendanceLocation.fromMap(locationValue);
      } else if (locationValue != null && locationValue.runtimeType.toString().contains('GeoPoint')) {
        // Firebase GeoPoint
        final geoPoint = locationValue as dynamic;
        return AttendanceLocation(
          latitude: geoPoint.latitude,
          longitude: geoPoint.longitude,
        );
      } else {
        return const AttendanceLocation(latitude: 0, longitude: 0);
      }
    }

    return Attendance(
      id: map['id'] as String,
      employeeId: map['employeeId'] as String,
      outletId: map['outletId'] as String,
      checkInTime: parseDate(map['checkInTime'] ?? map['clockInTime']),
      checkInLocation: parseLocation(map['checkInLocation'] ?? map['clockInLocation']),
      checkOutTime: map['checkOutTime'] != null || map['clockOutTime'] != null
          ? parseDate(map['checkOutTime'] ?? map['clockOutTime'])
          : null,
      checkOutLocation: map['checkOutLocation'] != null || map['clockOutLocation'] != null
          ? parseLocation(map['checkOutLocation'] ?? map['clockOutLocation'])
          : null,
      isSynced: map['isSynced'] as bool? ?? false,
      createdAt: parseDate(map['createdAt'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() => toMap();

  factory Attendance.fromJson(Map<String, dynamic> json) => Attendance.fromMap(json);

  bool get isComplete => checkOutTime != null && checkOutLocation != null;

  Duration get duration {
    if (checkOutTime == null) {
      return Duration.zero;
    }
    return checkOutTime!.difference(checkInTime);
  }

  // Backward compatibility getters
  DateTime get clockInTime => checkInTime;
  AttendanceLocation get clockInLocation => checkInLocation;
  DateTime? get clockOutTime => checkOutTime;
  AttendanceLocation? get clockOutLocation => checkOutLocation;

  @override
  List<Object?> get props => [
        id,
        employeeId,
        outletId,
        checkInTime,
        checkInLocation,
        checkOutTime,
        checkOutLocation,
        isSynced,
        createdAt,
      ];
}