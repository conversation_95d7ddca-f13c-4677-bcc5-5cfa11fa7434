import 'package:hive/hive.dart';
import 'package:pos_app/models/attendance.dart';

class AttendanceAdapter extends TypeAdapter<Attendance> {
  @override
  final int typeId = 3;

  @override
  Attendance read(BinaryReader reader) {
    final map = Map<String, dynamic>.from(reader.readMap());
    return Attendance.fromJson(map);
  }

  @override
  void write(BinaryWriter writer, Attendance obj) {
    final map = obj.toJson();
    writer.writeMap(map);
  }
}