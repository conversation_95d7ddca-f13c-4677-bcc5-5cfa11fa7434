import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:hive/hive.dart';
import 'package:pos_app/models/attendance.dart';

class AttendanceAdapter extends TypeAdapter<Attendance> {
  @override
  final int typeId = 3;

  @override
  Attendance read(BinaryReader reader) {
    final map = Map<String, dynamic>.from(reader.readMap());

    // Convert GeoPoint data
    final clockInLocationMap = map['clockInLocation'] as Map<String, dynamic>;
    map['clockInLocation'] = GeoPoint(
      clockInLocationMap['latitude'] as double,
      clockInLocationMap['longitude'] as double,
    );

    if (map['clockOutLocation'] != null) {
      final clockOutLocationMap = map['clockOutLocation'] as Map<String, dynamic>;
      map['clockOutLocation'] = GeoPoint(
        clockOutLocationMap['latitude'] as double,
        clockOutLocationMap['longitude'] as double,
      );
    }

    return Attendance.fromJson(map);
  }

  @override
  void write(BinaryWriter writer, Attendance obj) {
    final map = obj.toJson();

    // Convert AttendanceLocation to map for storage
    map['checkInLocation'] = obj.checkInLocation.toMap();

    if (obj.checkOutLocation != null) {
      map['checkOutLocation'] = obj.checkOutLocation!.toMap();
    }

    writer.writeMap(map);
  }
}