import 'package:hive/hive.dart';
import 'package:pos_app/models/sale.dart';

class SaleAdapter extends TypeAdapter<Sale> {
  @override
  final int typeId = 0;

  @override
  Sale read(BinaryReader reader) {
    return Sale.fromJson(Map<String, dynamic>.from(reader.readMap()));
  }

  @override
  void write(BinaryWriter writer, Sale obj) {
    writer.writeMap(obj.toJson());
  }
}

class SaleItemAdapter extends TypeAdapter<SaleItem> {
  @override
  final int typeId = 1;

  @override
  SaleItem read(BinaryReader reader) {
    return SaleItem.fromJson(Map<String, dynamic>.from(reader.readMap()));
  }

  @override
  void write(BinaryWriter writer, SaleItem obj) {
    writer.writeMap(obj.toJson());
  }
}

class PaymentMethodAdapter extends TypeAdapter<PaymentMethod> {
  @override
  final int typeId = 2;

  @override
  PaymentMethod read(BinaryReader reader) {
    return PaymentMethod.values[reader.readInt()];
  }

  @override
  void write(BinaryWriter writer, PaymentMethod obj) {
    writer.writeInt(obj.index);
  }
}