// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyA8X8Oo5cuTt01SQdy8caaVpDFji2EmDjc',
    appId: '1:339402194886:android:5c42083a37da4a9638fcc7',
    messagingSenderId: '339402194886',
    projectId: 'mudahkan-pos-app',
    storageBucket: 'mudahkan-pos-app.firebasestorage.app',
  );

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAO_FDTRyKs9lV2MMv1Z44uevHwm4VYVsw',
    appId: '1:339402194886:web:1fe698f528d89bdb38fcc7',
    messagingSenderId: '339402194886',
    projectId: 'mudahkan-pos-app',
    authDomain: 'mudahkan-pos-app.firebaseapp.com',
    storageBucket: 'mudahkan-pos-app.firebasestorage.app',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDJTUK8Xpqx70zquES_eZ2m_dxj4RLukSI',
    appId: '1:339402194886:ios:992cf3c5a7eaf22238fcc7',
    messagingSenderId: '339402194886',
    projectId: 'mudahkan-pos-app',
    storageBucket: 'mudahkan-pos-app.firebasestorage.app',
    iosBundleId: 'com.mudahkan.posApp',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDJTUK8Xpqx70zquES_eZ2m_dxj4RLukSI',
    appId: '1:339402194886:ios:992cf3c5a7eaf22238fcc7',
    messagingSenderId: '339402194886',
    projectId: 'mudahkan-pos-app',
    storageBucket: 'mudahkan-pos-app.firebasestorage.app',
    iosBundleId: 'com.mudahkan.posApp',
  );

}