class AppConstants {
  // Private constructor to prevent instantiation
  AppConstants._();
  
  // App Info
  static const String appName = 'POS & Outlet Management';
  static const String appVersion = '1.0.0';
  
  // Firebase Collections
  static const String usersCollection = 'users';
  static const String outletsCollection = 'outlets';
  static const String productsCollection = 'products';
  static const String salesCollection = 'sales';
  static const String attendanceCollection = 'attendance';
  
  // Hive Boxes
  static const String userBox = 'userBox';
  static const String outletBox = 'outletBox';
  static const String productBox = 'productBox';
  static const String saleBox = 'saleBox';
  static const String attendanceBox = 'attendanceBox';
  static const String configBox = 'configBox';
  
  // Shared Preferences Keys
  static const String isLoggedInKey = 'isLoggedIn';
  static const String userIdKey = 'userId';
  static const String userRoleKey = 'userRole';
  
  // Error Messages
  static const String networkErrorMessage = 'Network connection unavailable';
  static const String serverErrorMessage = 'Server error occurred';
  static const String unauthorizedErrorMessage = 'Unauthorized access';
  static const String unknownErrorMessage = 'Something went wrong';
  
  // Success Messages
  static const String loginSuccessMessage = 'Login successful';
  static const String logoutSuccessMessage = 'Logout successful';
  static const String saveSuccessMessage = 'Saved successfully';
  static const String syncSuccessMessage = 'Sync completed successfully';
  
  // Validation Messages
  static const String requiredFieldMessage = 'This field is required';
  static const String invalidEmailMessage = 'Invalid email address';
  static const String invalidPhoneMessage = 'Invalid phone number';
  static const String passwordMinLengthMessage = 'Password must be at least 6 characters';
  
  // Misc
  static const int syncIntervalMinutes = 15;
  static const double maxLocationDistanceMeters = 100.0;
}