import 'package:flutter/material.dart';

enum AppButtonType { primary, secondary, tertiary, outline, text }

class AppButton extends StatelessWidget {
  final String label;
  final VoidCallback onPressed;
  final AppButtonType type;
  final bool isLoading;
  final IconData? icon;
  final double? width;
  final double height;
  final bool disabled;

  const AppButton({
    super.key,
    required this.label,
    required this.onPressed,
    this.type = AppButtonType.primary,
    this.isLoading = false,
    this.icon,
    this.width,
    this.height = 50,
    this.disabled = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    // Button styling based on type
    Widget buttonWidget;
    
    switch (type) {
      case AppButtonType.primary:
        buttonWidget = _buildElevatedButton(
          context: context,
          backgroundColor: theme.colorScheme.primary,
          textColor: Colors.white,
        );
        break;
      case AppButtonType.secondary:
        buttonWidget = _buildElevatedButton(
          context: context,
          backgroundColor: theme.colorScheme.secondary,
          textColor: Colors.white,
        );
        break;
      case AppButtonType.tertiary:
        buttonWidget = _buildElevatedButton(
          context: context,
          backgroundColor: theme.colorScheme.tertiary,
          textColor: Colors.white,
        );
        break;
      case AppButtonType.outline:
        buttonWidget = _buildOutlineButton(context);
        break;
      case AppButtonType.text:
        buttonWidget = _buildTextButton(context);
        break;
    }
    
    return SizedBox(
      width: width,
      height: height,
      child: buttonWidget,
    );
  }
  
  Widget _buildElevatedButton({
    required BuildContext context,
    required Color backgroundColor,
    required Color textColor,
  }) {
    return ElevatedButton(
      onPressed: disabled || isLoading ? null : onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: backgroundColor,
        foregroundColor: textColor,
        disabledBackgroundColor: backgroundColor.withOpacity(0.5),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      child: _buildButtonContent(context, textColor),
    );
  }
  
  Widget _buildOutlineButton(BuildContext context) {
    final theme = Theme.of(context);
    
    return OutlinedButton(
      onPressed: disabled || isLoading ? null : onPressed,
      style: OutlinedButton.styleFrom(
        side: BorderSide(
          color: disabled
              ? theme.colorScheme.primary.withOpacity(0.5)
              : theme.colorScheme.primary,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      child: _buildButtonContent(
        context,
        disabled
            ? theme.colorScheme.primary.withOpacity(0.5)
            : theme.colorScheme.primary,
      ),
    );
  }
  
  Widget _buildTextButton(BuildContext context) {
    final theme = Theme.of(context);
    
    return TextButton(
      onPressed: disabled || isLoading ? null : onPressed,
      style: TextButton.styleFrom(
        foregroundColor: theme.colorScheme.primary,
        disabledForegroundColor: theme.colorScheme.primary.withOpacity(0.5),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      child: _buildButtonContent(
        context,
        disabled
            ? theme.colorScheme.primary.withOpacity(0.5)
            : theme.colorScheme.primary,
      ),
    );
  }
  
  Widget _buildButtonContent(BuildContext context, Color color) {
    if (isLoading) {
      return SizedBox(
        width: 24,
        height: 24,
        child: CircularProgressIndicator(
          color: color,
          strokeWidth: 2,
        ),
      );
    }
    
    if (icon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 20),
          const SizedBox(width: 8),
          Text(label),
        ],
      );
    }
    
    return Text(label);
  }
}