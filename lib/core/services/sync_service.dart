import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:pos_app/core/utils/logger.dart';
import 'package:pos_app/features/attendance/infrastructure/repositories/attendance_repository.dart';
import 'package:pos_app/features/pos/infrastructure/repositories/pos_repository.dart';

class SyncService extends StateNotifier<bool> {
  final POSRepository _posRepository;
  final AttendanceRepository _attendanceRepository;
  final InternetConnectionChecker _connectionChecker;

  SyncService(
    this._posRepository,
    this._attendanceRepository,
    this._connectionChecker,
  ) : super(false) {
    // Listen to connection changes
    _connectionChecker.onStatusChange.listen((status) {
      if (status == InternetConnectionStatus.connected) {
        syncData();
      }
    });
  }

  Future<void> syncData() async {
    if (state) return; // Already syncing
    
    try {
      state = true; // Start syncing
      AppLogger.info('Starting data sync...');

      // Sync sales data
      final salesResult = await _posRepository.syncOfflineSales();
      salesResult.fold(
        (failure) => AppLogger.error('Sales sync failed: ${failure.message}'),
        (_) => AppLogger.info('Sales sync completed'),
      );

      // Sync attendance data
      final attendanceResult = await _attendanceRepository.syncOfflineAttendances();
      attendanceResult.fold(
        (failure) => AppLogger.error('Attendance sync failed: ${failure.message}'),
        (_) => AppLogger.info('Attendance sync completed'),
      );

      AppLogger.info('Data sync completed');
    } catch (e) {
      AppLogger.error('Sync error', e);
    } finally {
      state = false; // End syncing
    }
  }
}