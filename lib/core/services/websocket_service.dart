import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:socket_io_client/socket_io_client.dart' as io;
import '../config/api_config.dart';
import '../utils/logger.dart';
import '../../models/sale.dart';
import '../../models/attendance.dart';

class WebSocketService {
  io.Socket? _socket;
  final StreamController<Sale> _newSaleController = StreamController<Sale>.broadcast();
  final StreamController<Attendance> _attendanceUpdateController = StreamController<Attendance>.broadcast();
  final StreamController<Map<String, dynamic>> _stockUpdateController = StreamController<Map<String, dynamic>>.broadcast();
  
  bool _isConnected = false;
  String? _currentOutletId;

  // Streams
  Stream<Sale> get newSaleStream => _newSaleController.stream;
  Stream<Attendance> get attendanceUpdateStream => _attendanceUpdateController.stream;
  Stream<Map<String, dynamic>> get stockUpdateStream => _stockUpdateController.stream;

  bool get isConnected => _isConnected;

  Future<void> connect({String? accessToken}) async {
    try {
      if (_socket != null && _isConnected) {
        AppLogger.info('WebSocket already connected');
        return;
      }

      final options = io.OptionBuilder()
          .setTransports(['websocket'])
          .enableAutoConnect()
          .enableReconnection()
          .setReconnectionAttempts(5)
          .setReconnectionDelay(1000);

      if (accessToken != null) {
        options.setAuth({'token': accessToken});
      }

      _socket = io.io(ApiConfig.websocketUrl, options.build());

      _socket!.onConnect((_) {
        _isConnected = true;
        AppLogger.info('WebSocket connected');
        
        // Join outlet room if we have an outlet ID
        if (_currentOutletId != null) {
          joinOutlet(_currentOutletId!);
        }
      });

      _socket!.onDisconnect((_) {
        _isConnected = false;
        AppLogger.info('WebSocket disconnected');
      });

      _socket!.onConnectError((error) {
        _isConnected = false;
        AppLogger.error('WebSocket connection error', error);
      });

      _socket!.onError((error) {
        AppLogger.error('WebSocket error', error);
      });

      // Listen for events
      _setupEventListeners();

      _socket!.connect();
    } catch (e) {
      AppLogger.error('WebSocket connect error', e);
    }
  }

  void _setupEventListeners() {
    if (_socket == null) return;

    // New sale event
    _socket!.on('new-sale', (data) {
      try {
        final sale = Sale.fromMap(data);
        _newSaleController.add(sale);
        AppLogger.info('Received new sale: ${sale.id}');
      } catch (e) {
        AppLogger.error('Error parsing new sale event', e);
      }
    });

    // Attendance update event
    _socket!.on('attendance-update', (data) {
      try {
        final attendance = Attendance.fromMap(data);
        _attendanceUpdateController.add(attendance);
        AppLogger.info('Received attendance update: ${attendance.id}');
      } catch (e) {
        AppLogger.error('Error parsing attendance update event', e);
      }
    });

    // Stock update event
    _socket!.on('stock-update', (data) {
      try {
        final stockData = Map<String, dynamic>.from(data);
        _stockUpdateController.add(stockData);
        AppLogger.info('Received stock update: ${stockData['productId']}');
      } catch (e) {
        AppLogger.error('Error parsing stock update event', e);
      }
    });

    // Connection status events
    _socket!.on('joined-outlet', (data) {
      AppLogger.info('Joined outlet: ${data['outletId']}');
    });

    _socket!.on('left-outlet', (data) {
      AppLogger.info('Left outlet: ${data['outletId']}');
    });
  }

  void joinOutlet(String outletId) {
    if (_socket != null && _isConnected) {
      _socket!.emit('join-outlet', {'outletId': outletId});
      _currentOutletId = outletId;
      AppLogger.info('Joining outlet: $outletId');
    } else {
      // Store outlet ID to join when connected
      _currentOutletId = outletId;
      AppLogger.warning('WebSocket not connected, will join outlet when connected');
    }
  }

  void leaveOutlet(String outletId) {
    if (_socket != null && _isConnected) {
      _socket!.emit('leave-outlet', {'outletId': outletId});
      AppLogger.info('Leaving outlet: $outletId');
    }
    
    if (_currentOutletId == outletId) {
      _currentOutletId = null;
    }
  }

  void disconnect() {
    try {
      if (_socket != null) {
        _socket!.disconnect();
        _socket!.dispose();
        _socket = null;
      }
      
      _isConnected = false;
      _currentOutletId = null;
      
      AppLogger.info('WebSocket disconnected manually');
    } catch (e) {
      AppLogger.error('WebSocket disconnect error', e);
    }
  }

  void dispose() {
    disconnect();
    _newSaleController.close();
    _attendanceUpdateController.close();
    _stockUpdateController.close();
  }

  // Send custom events (if needed)
  void emit(String event, dynamic data) {
    if (_socket != null && _isConnected) {
      _socket!.emit(event, data);
    } else {
      AppLogger.warning('Cannot emit event: WebSocket not connected');
    }
  }

  // Reconnect with new token
  Future<void> reconnectWithToken(String accessToken) async {
    disconnect();
    await Future.delayed(const Duration(milliseconds: 500));
    await connect(accessToken: accessToken);
  }
}

// Provider for WebSocketService
final webSocketServiceProvider = Provider<WebSocketService>((ref) {
  final service = WebSocketService();
  
  // Dispose when provider is disposed
  ref.onDispose(() {
    service.dispose();
  });
  
  return service;
});

// Provider for connection status
final webSocketConnectionProvider = StreamProvider<bool>((ref) {
  final service = ref.watch(webSocketServiceProvider);
  
  // Create a stream that emits connection status
  return Stream.periodic(const Duration(seconds: 1))
      .map((_) => service.isConnected)
      .distinct();
});

// Provider for new sales stream
final newSaleStreamProvider = StreamProvider<Sale>((ref) {
  final service = ref.watch(webSocketServiceProvider);
  return service.newSaleStream;
});

// Provider for attendance updates stream
final attendanceUpdateStreamProvider = StreamProvider<Attendance>((ref) {
  final service = ref.watch(webSocketServiceProvider);
  return service.attendanceUpdateStream;
});

// Provider for stock updates stream
final stockUpdateStreamProvider = StreamProvider<Map<String, dynamic>>((ref) {
  final service = ref.watch(webSocketServiceProvider);
  return service.stockUpdateStream;
});
