import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../config/api_config.dart';
import '../errors/failures.dart';
import '../utils/logger.dart';
import '../../models/user.dart';
import 'http_client_service.dart';

abstract class AuthService {
  Future<Either<Failure, User>> signIn(String email, String password);
  Future<Either<Failure, void>> signOut();
  Future<Either<Failure, bool>> isSignedIn();
  Future<Either<Failure, User>> getCurrentUser();
  Future<Either<Failure, String>> refreshToken();
}

class AuthServiceHttp implements AuthService {
  final HttpClientService _httpClient;

  AuthServiceHttp(this._httpClient);

  @override
  Future<Either<Failure, User>> signIn(String email, String password) async {
    try {
      final response = await _httpClient.post(
        ApiConfig.login,
        data: {
          'email': email,
          'password': password,
        },
      );

      if (response.statusCode == 200) {
        final data = response.data['data'];
        
        // Store tokens
        await _httpClient.setTokens(
          data['accessToken'],
          data['refreshToken'],
        );
        
        // Parse user data
        final user = User.fromMap(data['user']);
        
        AppLogger.info('User signed in: ${user.email}');
        return Right(user);
      } else {
        return Left(AuthFailure('Login failed: ${response.statusMessage}'));
      }
    } catch (e) {
      AppLogger.error('Sign in error', e);
      
      if (e.toString().contains('401')) {
        return const Left(AuthFailure('Invalid email or password'));
      } else if (e.toString().contains('429')) {
        return const Left(AuthFailure('Too many login attempts. Try again later'));
      } else if (e.toString().contains('network') || e.toString().contains('connection')) {
        return const Left(NetworkFailure('Network error. Please check your connection'));
      } else {
        return Left(UnknownFailure(e.toString()));
      }
    }
  }

  @override
  Future<Either<Failure, void>> signOut() async {
    try {
      final refreshToken = await _httpClient.getRefreshToken();
      
      if (refreshToken != null) {
        // Call logout endpoint to invalidate tokens
        await _httpClient.post(
          ApiConfig.logout,
          data: {'refreshToken': refreshToken},
        );
      }
      
      // Clear local storage
      await _httpClient.clearTokens();
      
      AppLogger.info('User signed out');
      return const Right(null);
    } catch (e) {
      AppLogger.error('Sign out error', e);
      
      // Even if logout fails, clear local storage
      await _httpClient.clearTokens();
      return const Right(null);
    }
  }

  @override
  Future<Either<Failure, bool>> isSignedIn() async {
    try {
      final isValid = await _httpClient.isTokenValid();
      
      if (!isValid) {
        // Try to refresh token
        final refreshResult = await refreshToken();
        return Right(refreshResult.isRight());
      }
      
      return const Right(true);
    } catch (e) {
      AppLogger.error('Check sign in status error', e);
      return const Right(false);
    }
  }

  @override
  Future<Either<Failure, User>> getCurrentUser() async {
    try {
      final response = await _httpClient.get(ApiConfig.profile);
      
      if (response.statusCode == 200) {
        final user = User.fromMap(response.data['data']);
        return Right(user);
      } else {
        return const Left(AuthFailure('Failed to get user profile'));
      }
    } catch (e) {
      AppLogger.error('Get current user error', e);
      
      if (e.toString().contains('401')) {
        return const Left(AuthFailure('User not authenticated'));
      } else {
        return Left(UnknownFailure(e.toString()));
      }
    }
  }

  @override
  Future<Either<Failure, String>> refreshToken() async {
    try {
      final refreshToken = await _httpClient.getRefreshToken();
      
      if (refreshToken == null) {
        return const Left(AuthFailure('No refresh token available'));
      }
      
      final response = await _httpClient.post(
        ApiConfig.refresh,
        data: {'refreshToken': refreshToken},
      );
      
      if (response.statusCode == 200) {
        final data = response.data['data'];
        final newAccessToken = data['accessToken'];
        
        // Update access token
        await _httpClient.setTokens(newAccessToken, refreshToken);
        
        AppLogger.info('Token refreshed successfully');
        return Right(newAccessToken);
      } else {
        return const Left(AuthFailure('Token refresh failed'));
      }
    } catch (e) {
      AppLogger.error('Refresh token error', e);
      
      if (e.toString().contains('401')) {
        // Refresh token is invalid, user needs to login again
        await _httpClient.clearTokens();
        return const Left(AuthFailure('Session expired. Please login again'));
      }
      
      return Left(UnknownFailure(e.toString()));
    }
  }

  // Additional helper methods
  Future<Either<Failure, User>> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      final user = await getCurrentUser();
      if (user.isLeft()) return user;
      
      final userId = user.getOrElse(() => throw Exception()).id;
      
      final response = await _httpClient.patch(
        '${ApiConfig.users}/$userId/change-password',
        data: {
          'currentPassword': currentPassword,
          'newPassword': newPassword,
        },
      );
      
      if (response.statusCode == 204) {
        AppLogger.info('Password changed successfully');
        return await getCurrentUser();
      } else {
        return const Left(AuthFailure('Failed to change password'));
      }
    } catch (e) {
      AppLogger.error('Change password error', e);
      
      if (e.toString().contains('401')) {
        return const Left(AuthFailure('Current password is incorrect'));
      } else {
        return Left(UnknownFailure(e.toString()));
      }
    }
  }

  Future<Either<Failure, User>> updateProfile({
    String? name,
    String? phone,
    String? profileImage,
  }) async {
    try {
      final user = await getCurrentUser();
      if (user.isLeft()) return user;
      
      final userId = user.getOrElse(() => throw Exception()).id;
      
      final updateData = <String, dynamic>{};
      if (name != null) updateData['name'] = name;
      if (phone != null) updateData['phone'] = phone;
      if (profileImage != null) updateData['profileImage'] = profileImage;
      
      if (updateData.isEmpty) {
        return user; // No changes
      }
      
      final response = await _httpClient.patch(
        '${ApiConfig.users}/$userId',
        data: updateData,
      );
      
      if (response.statusCode == 200) {
        final updatedUser = User.fromMap(response.data['data']);
        AppLogger.info('Profile updated successfully');
        return Right(updatedUser);
      } else {
        return const Left(AuthFailure('Failed to update profile'));
      }
    } catch (e) {
      AppLogger.error('Update profile error', e);
      return Left(UnknownFailure(e.toString()));
    }
  }
}

// Provider for AuthService
final authServiceProvider = Provider<AuthService>((ref) {
  final httpClient = ref.watch(httpClientServiceProvider);
  return AuthServiceHttp(httpClient);
});
