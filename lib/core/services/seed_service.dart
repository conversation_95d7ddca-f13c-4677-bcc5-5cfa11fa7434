import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:pos_app/core/constants/app_constants.dart';
import 'package:pos_app/core/utils/logger.dart';
import 'package:pos_app/models/outlet.dart';
import 'package:pos_app/models/product.dart';
import 'package:pos_app/models/user.dart' as app_user;

class SeedService {
  final FirebaseFirestore _firestore;
  final firebase_auth.FirebaseAuth _auth;

  SeedService({
    required FirebaseFirestore firestore,
    required firebase_auth.FirebaseAuth auth,
  })  : _firestore = firestore,
        _auth = auth;

  Future<void> seedDatabase() async {
    try {
      AppLogger.info('Starting database seeding...');

      // Create admin user
      final adminUser = await _createAdminUser();
      AppLogger.info('Admin user created: ${adminUser.email}');

      // Create outlets
      final outlets = await _createOutlets();
      AppLogger.info('Outlets created: ${outlets.length}');

      // Create employees for each outlet
      for (final outlet in outlets) {
        final employees = await _createEmployees(outlet);
        AppLogger.info('Employees created for ${outlet.name}: ${employees.length}');
      }

      // Create products
      final products = await _createProducts(outlets);
      AppLogger.info('Products created: ${products.length}');

      AppLogger.info('Database seeding completed successfully');
    } catch (e) {
      AppLogger.error('Database seeding failed', e);
      rethrow;
    }
  }

  Future<app_user.User> _createAdminUser() async {
    const email = '<EMAIL>';
    const password = 'admin123';

    // Create Firebase Auth user
    final userCredential = await _auth.createUserWithEmailAndPassword(
      email: email,
      password: password,
    );

    final admin = app_user.User(
      id: userCredential.user!.uid,
      name: 'Admin',
      email: email,
      phone: '+6281234567890',
      role: app_user.UserRole.admin,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    await _firestore
        .collection(AppConstants.usersCollection)
        .doc(admin.id)
        .set(admin.toMap());

    return admin;
  }

  Future<List<Outlet>> _createOutlets() async {
    final outlets = [
      Outlet(
        id: 'outlet-1',
        name: 'Main Store',
        address: 'Jl. Sudirman No. 123, Jakarta',
        location: const GeoPoint(-6.2088, 106.8456), // Jakarta coordinates
        phone: '+6281234567891',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      Outlet(
        id: 'outlet-2',
        name: 'Branch Store',
        address: 'Jl. Thamrin No. 456, Jakarta',
        location: const GeoPoint(-6.2000, 106.8200), // Jakarta coordinates
        phone: '+6281234567892',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    ];

    for (final outlet in outlets) {
      await _firestore
          .collection(AppConstants.outletsCollection)
          .doc(outlet.id)
          .set(outlet.toMap());
    }

    return outlets;
  }

  Future<List<app_user.User>> _createEmployees(Outlet outlet) async {
    final employees = [
      {
        'name': 'John Doe',
        'email': '<EMAIL>',
        'phone': '+6281234567893',
        'password': 'employee123',
      },
      {
        'name': 'Jane Smith',
        'email': '<EMAIL>',
        'phone': '+6281234567894',
        'password': 'employee123',
      },
    ];

    final createdEmployees = <app_user.User>[];

    for (final employee in employees) {
      // Create Firebase Auth user
      final userCredential = await _auth.createUserWithEmailAndPassword(
        email: employee['email']!,
        password: employee['password']!,
      );

      final newEmployee = app_user.User(
        id: userCredential.user!.uid,
        name: employee['name']!,
        email: employee['email']!,
        phone: employee['phone']!,
        role: app_user.UserRole.employee,
        outletId: outlet.id,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(newEmployee.id)
          .set(newEmployee.toMap());

      createdEmployees.add(newEmployee);
    }

    return createdEmployees;
  }

  Future<List<Product>> _createProducts(List<Outlet> outlets) async {
    final products = [
      {
        'id': 'product-1',
        'name': 'Coffee Latte',
        'description': 'Smooth and creamy coffee with steamed milk',
        'price': 25000.0,
        'category': 'Beverages',
        'stock': {for (var o in outlets) o.id: 50},
      },
      {
        'id': 'product-2',
        'name': 'Cappuccino',
        'description': 'Classic Italian coffee with foamed milk',
        'price': 28000.0,
        'category': 'Beverages',
        'stock': {for (var o in outlets) o.id: 50},
      },
      {
        'id': 'product-3',
        'name': 'Croissant',
        'description': 'Buttery and flaky French pastry',
        'price': 15000.0,
        'category': 'Pastries',
        'stock': {for (var o in outlets) o.id: 30},
      },
      {
        'id': 'product-4',
        'name': 'Chocolate Cake',
        'description': 'Rich and moist chocolate cake',
        'price': 35000.0,
        'category': 'Cakes',
        'stock': {for (var o in outlets) o.id: 20},
      },
    ];

    final createdProducts = <Product>[];

    for (final product in products) {
      final newProduct = Product(
        id: product['id'] as String,
        name: product['name'] as String,
        description: product['description'] as String,
        price: product['price'] as double,
        category: product['category'] as String,
        stock: Map<String, int>.from(product['stock'] as Map),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _firestore
          .collection(AppConstants.productsCollection)
          .doc(newProduct.id)
          .set(newProduct.toMap());

      createdProducts.add(newProduct);
    }

    return createdProducts;
  }
}