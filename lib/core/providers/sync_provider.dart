import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:pos_app/core/services/sync_service.dart';
import 'package:pos_app/features/attendance/application/providers/attendance_providers.dart';
import 'package:pos_app/features/pos/application/providers/pos_providers.dart';

// Connection Provider
final connectionProvider = StreamProvider<bool>((ref) {
  return InternetConnectionChecker.instance.onStatusChange.map(
    (status) => status == InternetConnectionStatus.connected,
  );
});

// Sync Service Provider
final syncServiceProvider = StateNotifierProvider<SyncService, bool>((ref) {
  return SyncService(
    ref.watch(posRepositoryProvider),
    ref.watch(attendanceRepositoryProvider),
    InternetConnectionChecker.instance,
  );
});