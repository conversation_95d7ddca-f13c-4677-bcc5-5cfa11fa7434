import 'package:flutter/material.dart';
import 'package:pos_app/features/auth/presentation/screens/login_screen.dart';
import 'package:pos_app/features/auth/presentation/screens/splash_screen.dart';
import 'package:pos_app/features/dashboard/presentation/screens/dashboard_screen.dart';
import 'package:pos_app/features/employee/presentation/screens/employee_form_screen.dart';
import 'package:pos_app/features/employee/presentation/screens/employee_list_screen.dart';
import 'package:pos_app/features/outlet/presentation/screens/outlet_form_screen.dart';
import 'package:pos_app/features/outlet/presentation/screens/outlet_list_screen.dart';
import 'package:pos_app/features/product/presentation/screens/product_form_screen.dart';
import 'package:pos_app/features/product/presentation/screens/product_list_screen.dart';
import 'package:pos_app/features/pos/presentation/screens/pos_screen.dart';
import 'package:pos_app/features/attendance/presentation/screens/attendance_screen.dart';
import 'package:pos_app/features/reports/presentation/screens/reports_screen.dart';
import 'package:pos_app/models/outlet.dart';
import 'package:pos_app/models/product.dart';
import 'package:pos_app/models/user.dart';

class AppRouter {
  static Route<dynamic> onGenerateRoute(RouteSettings settings) {
    switch (settings.name) {
      case SplashScreen.routeName:
        return MaterialPageRoute(
          builder: (_) => const SplashScreen(),
        );
      case LoginScreen.routeName:
        return MaterialPageRoute(
          builder: (_) => const LoginScreen(),
        );
      case DashboardScreen.routeName:
        return MaterialPageRoute(
          builder: (_) => const DashboardScreen(),
        );
      case POSScreen.routeName:
        return MaterialPageRoute(
          builder: (_) => const POSScreen(),
        );
      case AttendanceScreen.routeName:
        return MaterialPageRoute(
          builder: (_) => const AttendanceScreen(),
        );
      case ReportsScreen.routeName:
        return MaterialPageRoute(
          builder: (_) => const ReportsScreen(),
        );
      case OutletListScreen.routeName:
        return MaterialPageRoute(
          builder: (_) => const OutletListScreen(),
        );
      case OutletFormScreen.routeName:
        final outlet = settings.arguments as Outlet?;
        return MaterialPageRoute(
          builder: (_) => OutletFormScreen(outlet: outlet),
        );
      case EmployeeListScreen.routeName:
        return MaterialPageRoute(
          builder: (_) => const EmployeeListScreen(),
        );
      case EmployeeFormScreen.routeName:
        final employee = settings.arguments as User?;
        return MaterialPageRoute(
          builder: (_) => EmployeeFormScreen(employee: employee),
        );
      case ProductListScreen.routeName:
        return MaterialPageRoute(
          builder: (_) => const ProductListScreen(),
        );
      case ProductFormScreen.routeName:
        final product = settings.arguments as Product?;
        return MaterialPageRoute(
          builder: (_) => ProductFormScreen(product: product),
        );
      default:
        return MaterialPageRoute(
          builder: (_) => Scaffold(
            body: Center(
              child: Text('No route defined for ${settings.name}'),
            ),
          ),
        );
    }
  }
}