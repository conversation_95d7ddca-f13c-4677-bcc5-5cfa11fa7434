class ApiConfig {
  // Base URLs for different environments
  static const String _baseUrlDev = 'http://localhost:3000/api/v1';
  static const String _baseUrlStaging = 'https://staging-api.yourapp.com/api/v1';
  static const String _baseUrlProd = 'https://api.yourapp.com/api/v1';
  
  // Current environment
  static const String _environment = String.fromEnvironment(
    'ENVIRONMENT',
    defaultValue: 'development',
  );
  
  // Get base URL based on environment
  static String get baseUrl {
    switch (_environment) {
      case 'production':
        return _baseUrlProd;
      case 'staging':
        return _baseUrlStaging;
      case 'development':
      default:
        return _baseUrlDev;
    }
  }
  
  // WebSocket URL
  static String get websocketUrl {
    return baseUrl.replaceFirst('/api/v1', '').replaceFirst('http', 'ws');
  }
  
  // API Endpoints
  static const String auth = '/auth';
  static const String users = '/users';
  static const String outlets = '/outlets';
  static const String products = '/products';
  static const String sales = '/sales';
  static const String attendance = '/attendance';
  static const String reports = '/reports';
  
  // Auth endpoints
  static const String login = '$auth/login';
  static const String refresh = '$auth/refresh';
  static const String logout = '$auth/logout';
  static const String profile = '$auth/profile';
  
  // Sales endpoints
  static const String salesBatch = '$sales/batch';
  static const String salesReports = '$sales/reports';
  static const String salesDaily = '$salesReports/daily';
  static const String salesSummary = '$salesReports/summary';
  
  // Products endpoints
  static const String productsCategories = '$products/categories';
  static const String productsStats = '$products/stats';
  static const String productsLowStock = '$products/low-stock';
  
  // Users endpoints
  static const String usersStats = '$users/stats';
  
  // Attendance endpoints
  static const String attendanceCheckIn = '$attendance/check-in';
  static const String attendanceCheckOut = '$attendance/check-out';
  
  // Request timeouts
  static const Duration connectTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  static const Duration sendTimeout = Duration(seconds: 30);
  
  // Headers
  static const Map<String, String> defaultHeaders = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };
  
  // File upload
  static const int maxFileSize = 5 * 1024 * 1024; // 5MB
  static const List<String> allowedImageTypes = [
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
  ];
}
