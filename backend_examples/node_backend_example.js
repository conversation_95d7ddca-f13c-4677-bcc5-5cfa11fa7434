// Node.js Backend Example - Main Server Setup
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const { Pool } = require('pg');
const redis = require('redis');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const { body, validationResult } = require('express-validator');

const app = express();
const PORT = process.env.PORT || 3000;

// Database connection
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'pos_db',
  password: process.env.DB_PASSWORD || 'password',
  port: process.env.DB_PORT || 5432,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
});

// Redis client for caching and sessions
const redisClient = redis.createClient({
  host: process.env.REDIS_HOST || 'localhost',
  port: process.env.REDIS_PORT || 6379,
});

// Middleware
app.use(helmet());
app.use(cors({
  origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
  credentials: true,
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.',
});
app.use('/api/', limiter);

// Auth middleware
const authenticateToken = async (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
    
    // Check if token is blacklisted
    const isBlacklisted = await redisClient.get(`blacklist_${token}`);
    if (isBlacklisted) {
      return res.status(401).json({ error: 'Token has been revoked' });
    }

    req.user = decoded;
    next();
  } catch (error) {
    return res.status(403).json({ error: 'Invalid or expired token' });
  }
};

// Validation middleware
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

// Auth Routes
app.post('/api/v1/auth/login', [
  body('email').isEmail().normalizeEmail(),
  body('password').isLength({ min: 6 }),
  handleValidationErrors,
], async (req, res) => {
  try {
    const { email, password } = req.body;

    // Get user from database
    const userQuery = 'SELECT * FROM users WHERE email = $1 AND is_active = true';
    const userResult = await pool.query(userQuery, [email]);

    if (userResult.rows.length === 0) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    const user = userResult.rows[0];

    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.password_hash);
    if (!isValidPassword) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Generate tokens
    const accessToken = jwt.sign(
      { 
        userId: user.id, 
        email: user.email, 
        role: user.role,
        outletId: user.outlet_id 
      },
      process.env.JWT_SECRET || 'your-secret-key',
      { expiresIn: '15m' }
    );

    const refreshToken = jwt.sign(
      { userId: user.id },
      process.env.JWT_REFRESH_SECRET || 'your-refresh-secret',
      { expiresIn: '7d' }
    );

    // Store refresh token in Redis
    await redisClient.setex(`refresh_${user.id}`, 7 * 24 * 60 * 60, refreshToken);

    // Remove password from response
    const { password_hash, ...userWithoutPassword } = user;

    res.json({
      access_token: accessToken,
      refresh_token: refreshToken,
      user: userWithoutPassword,
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.post('/api/v1/auth/refresh', async (req, res) => {
  try {
    const { refresh_token } = req.body;

    if (!refresh_token) {
      return res.status(401).json({ error: 'Refresh token required' });
    }

    const decoded = jwt.verify(refresh_token, process.env.JWT_REFRESH_SECRET || 'your-refresh-secret');
    
    // Check if refresh token exists in Redis
    const storedToken = await redisClient.get(`refresh_${decoded.userId}`);
    if (storedToken !== refresh_token) {
      return res.status(401).json({ error: 'Invalid refresh token' });
    }

    // Get user data
    const userQuery = 'SELECT * FROM users WHERE id = $1 AND is_active = true';
    const userResult = await pool.query(userQuery, [decoded.userId]);

    if (userResult.rows.length === 0) {
      return res.status(401).json({ error: 'User not found' });
    }

    const user = userResult.rows[0];

    // Generate new access token
    const accessToken = jwt.sign(
      { 
        userId: user.id, 
        email: user.email, 
        role: user.role,
        outletId: user.outlet_id 
      },
      process.env.JWT_SECRET || 'your-secret-key',
      { expiresIn: '15m' }
    );

    res.json({ access_token: accessToken });
  } catch (error) {
    console.error('Refresh token error:', error);
    res.status(401).json({ error: 'Invalid refresh token' });
  }
});

app.post('/api/v1/auth/logout', authenticateToken, async (req, res) => {
  try {
    const { refresh_token } = req.body;
    const authHeader = req.headers['authorization'];
    const accessToken = authHeader && authHeader.split(' ')[1];

    // Blacklist access token
    if (accessToken) {
      const decoded = jwt.decode(accessToken);
      const expiresIn = decoded.exp - Math.floor(Date.now() / 1000);
      if (expiresIn > 0) {
        await redisClient.setex(`blacklist_${accessToken}`, expiresIn, 'true');
      }
    }

    // Remove refresh token
    if (refresh_token) {
      await redisClient.del(`refresh_${req.user.userId}`);
    }

    res.json({ message: 'Logged out successfully' });
  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Sales Routes
app.post('/api/v1/sales', authenticateToken, [
  body('outlet_id').isUUID(),
  body('items').isArray({ min: 1 }),
  body('subtotal').isNumeric(),
  body('tax').isNumeric(),
  body('grand_total').isNumeric(),
  body('payment_method').isIn(['cash', 'card', 'digital_wallet']),
  handleValidationErrors,
], async (req, res) => {
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');

    const {
      outlet_id,
      items,
      subtotal,
      tax,
      discount = 0,
      grand_total,
      payment_method,
      sale_date = new Date(),
    } = req.body;

    // Insert sale
    const saleQuery = `
      INSERT INTO sales (outlet_id, employee_id, subtotal, tax, discount, grand_total, payment_method, sale_date)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING *
    `;
    
    const saleResult = await client.query(saleQuery, [
      outlet_id,
      req.user.userId,
      subtotal,
      tax,
      discount,
      grand_total,
      payment_method,
      sale_date,
    ]);

    const sale = saleResult.rows[0];

    // Insert sale items
    const itemsQuery = `
      INSERT INTO sale_items (sale_id, product_id, product_name, price, quantity, total)
      VALUES ($1, $2, $3, $4, $5, $6)
    `;

    for (const item of items) {
      await client.query(itemsQuery, [
        sale.id,
        item.product_id,
        item.product_name,
        item.price,
        item.quantity,
        item.total,
      ]);
    }

    await client.query('COMMIT');

    // Get complete sale with items
    const completeSaleQuery = `
      SELECT s.*, 
             json_agg(
               json_build_object(
                 'id', si.id,
                 'product_id', si.product_id,
                 'product_name', si.product_name,
                 'price', si.price,
                 'quantity', si.quantity,
                 'total', si.total
               )
             ) as items
      FROM sales s
      LEFT JOIN sale_items si ON s.id = si.sale_id
      WHERE s.id = $1
      GROUP BY s.id
    `;

    const completeSaleResult = await client.query(completeSaleQuery, [sale.id]);
    
    res.status(201).json(completeSaleResult.rows[0]);
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Create sale error:', error);
    res.status(500).json({ error: 'Failed to create sale' });
  } finally {
    client.release();
  }
});

// Batch sales endpoint for offline sync
app.post('/api/v1/sales/batch', authenticateToken, async (req, res) => {
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');
    
    const { sales } = req.body;
    const createdSales = [];

    for (const saleData of sales) {
      // Similar logic as single sale creation
      // ... (implementation details)
      createdSales.push(saleData);
    }

    await client.query('COMMIT');
    res.status(201).json({ data: createdSales });
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Batch sales error:', error);
    res.status(500).json({ error: 'Failed to process batch sales' });
  } finally {
    client.release();
  }
});

// Health check endpoint
app.get('/api/v1/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    version: '1.0.0' 
  });
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Unhandled error:', error);
  res.status(500).json({ error: 'Internal server error' });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Route not found' });
});

// Start server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});

module.exports = app;
