// NestJS Sales Module Example
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SalesController } from './sales.controller';
import { SalesService } from './sales.service';
import { Sale } from './entities/sale.entity';
import { SaleItem } from './entities/sale-item.entity';
import { Product } from '../products/entities/product.entity';
import { User } from '../users/entities/user.entity';
import { Outlet } from '../outlets/entities/outlet.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Sale, SaleItem, Product, User, Outlet])],
  controllers: [SalesController],
  providers: [SalesService],
  exports: [SalesService],
})
export class SalesModule {}

// Sales Controller
import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { SalesService } from './sales.service';
import { CreateSaleDto } from './dto/create-sale.dto';
import { BatchSalesDto } from './dto/batch-sales.dto';
import { SalesQueryDto } from './dto/sales-query.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../common/decorators/roles.decorator';
import { GetUser } from '../common/decorators/get-user.decorator';
import { User } from '../users/entities/user.entity';
import { UserRole } from '../users/enums/user-role.enum';

@ApiTags('Sales')
@Controller('sales')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class SalesController {
  constructor(private readonly salesService: SalesService) {}

  @Post()
  @Roles(UserRole.ADMIN, UserRole.EMPLOYEE)
  @ApiOperation({ summary: 'Create a new sale' })
  @ApiResponse({ status: 201, description: 'Sale created successfully' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  async createSale(
    @Body() createSaleDto: CreateSaleDto,
    @GetUser() user: User,
  ) {
    return this.salesService.createSale(createSaleDto, user);
  }

  @Post('batch')
  @Roles(UserRole.ADMIN, UserRole.EMPLOYEE)
  @ApiOperation({ summary: 'Create multiple sales (offline sync)' })
  @ApiResponse({ status: 201, description: 'Batch sales created successfully' })
  async createBatchSales(
    @Body() batchSalesDto: BatchSalesDto,
    @GetUser() user: User,
  ) {
    return this.salesService.createBatchSales(batchSalesDto, user);
  }

  @Get()
  @Roles(UserRole.ADMIN, UserRole.EMPLOYEE)
  @ApiOperation({ summary: 'Get sales with pagination and filters' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'outletId', required: false, type: String })
  @ApiQuery({ name: 'startDate', required: false, type: String })
  @ApiQuery({ name: 'endDate', required: false, type: String })
  async getSales(@Query() query: SalesQueryDto, @GetUser() user: User) {
    return this.salesService.getSales(query, user);
  }

  @Get(':id')
  @Roles(UserRole.ADMIN, UserRole.EMPLOYEE)
  @ApiOperation({ summary: 'Get sale by ID' })
  @ApiResponse({ status: 200, description: 'Sale found' })
  @ApiResponse({ status: 404, description: 'Sale not found' })
  async getSaleById(
    @Param('id', ParseUUIDPipe) id: string,
    @GetUser() user: User,
  ) {
    return this.salesService.getSaleById(id, user);
  }

  @Get('reports/daily')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Get daily sales report' })
  async getDailySalesReport(
    @Query('date') date: string,
    @Query('outletId') outletId?: string,
  ) {
    return this.salesService.getDailySalesReport(date, outletId);
  }

  @Get('reports/summary')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Get sales summary' })
  async getSalesSummary(
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
    @Query('outletId') outletId?: string,
  ) {
    return this.salesService.getSalesSummary(startDate, endDate, outletId);
  }
}

// Sales Service
import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, Between } from 'typeorm';
import { Sale } from './entities/sale.entity';
import { SaleItem } from './entities/sale-item.entity';
import { Product } from '../products/entities/product.entity';
import { User } from '../users/entities/user.entity';
import { CreateSaleDto } from './dto/create-sale.dto';
import { BatchSalesDto } from './dto/batch-sales.dto';
import { SalesQueryDto } from './dto/sales-query.dto';
import { UserRole } from '../users/enums/user-role.enum';
import { PaymentMethod } from './enums/payment-method.enum';

@Injectable()
export class SalesService {
  constructor(
    @InjectRepository(Sale)
    private readonly saleRepository: Repository<Sale>,
    @InjectRepository(SaleItem)
    private readonly saleItemRepository: Repository<SaleItem>,
    @InjectRepository(Product)
    private readonly productRepository: Repository<Product>,
    private readonly dataSource: DataSource,
  ) {}

  async createSale(createSaleDto: CreateSaleDto, user: User): Promise<Sale> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Validate outlet access for employees
      if (user.role === UserRole.EMPLOYEE && user.outletId !== createSaleDto.outletId) {
        throw new ForbiddenException('Access denied to this outlet');
      }

      // Validate products and calculate totals
      await this.validateSaleItems(createSaleDto.items);

      // Create sale entity
      const sale = this.saleRepository.create({
        outletId: createSaleDto.outletId,
        employeeId: user.id,
        subtotal: createSaleDto.subtotal,
        tax: createSaleDto.tax,
        discount: createSaleDto.discount || 0,
        grandTotal: createSaleDto.grandTotal,
        paymentMethod: createSaleDto.paymentMethod,
        saleDate: createSaleDto.saleDate || new Date(),
        isSynced: true,
      });

      const savedSale = await queryRunner.manager.save(sale);

      // Create sale items
      const saleItems = createSaleDto.items.map(item =>
        this.saleItemRepository.create({
          saleId: savedSale.id,
          productId: item.productId,
          productName: item.productName,
          price: item.price,
          quantity: item.quantity,
          total: item.total,
        }),
      );

      await queryRunner.manager.save(saleItems);

      // Update product stock (if needed)
      for (const item of createSaleDto.items) {
        await queryRunner.manager.decrement(
          Product,
          { id: item.productId },
          'stockQuantity',
          item.quantity,
        );
      }

      await queryRunner.commitTransaction();

      // Return sale with items
      return this.getSaleById(savedSale.id, user);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async createBatchSales(batchSalesDto: BatchSalesDto, user: User) {
    const results = [];
    const errors = [];

    for (let i = 0; i < batchSalesDto.sales.length; i++) {
      try {
        const sale = await this.createSale(batchSalesDto.sales[i], user);
        results.push(sale);
      } catch (error) {
        errors.push({
          index: i,
          error: error.message,
          sale: batchSalesDto.sales[i],
        });
      }
    }

    return {
      success: results.length,
      failed: errors.length,
      data: results,
      errors,
    };
  }

  async getSales(query: SalesQueryDto, user: User) {
    const {
      page = 1,
      limit = 20,
      outletId,
      startDate,
      endDate,
      employeeId,
    } = query;

    const queryBuilder = this.saleRepository
      .createQueryBuilder('sale')
      .leftJoinAndSelect('sale.items', 'items')
      .leftJoinAndSelect('sale.employee', 'employee')
      .leftJoinAndSelect('sale.outlet', 'outlet')
      .orderBy('sale.saleDate', 'DESC');

    // Apply filters based on user role
    if (user.role === UserRole.EMPLOYEE) {
      queryBuilder.andWhere('sale.outletId = :userOutletId', {
        userOutletId: user.outletId,
      });
    }

    if (outletId) {
      queryBuilder.andWhere('sale.outletId = :outletId', { outletId });
    }

    if (employeeId) {
      queryBuilder.andWhere('sale.employeeId = :employeeId', { employeeId });
    }

    if (startDate && endDate) {
      queryBuilder.andWhere('sale.saleDate BETWEEN :startDate AND :endDate', {
        startDate: new Date(startDate),
        endDate: new Date(endDate),
      });
    }

    // Pagination
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    const [sales, total] = await queryBuilder.getManyAndCount();

    return {
      data: sales,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async getSaleById(id: string, user: User): Promise<Sale> {
    const queryBuilder = this.saleRepository
      .createQueryBuilder('sale')
      .leftJoinAndSelect('sale.items', 'items')
      .leftJoinAndSelect('sale.employee', 'employee')
      .leftJoinAndSelect('sale.outlet', 'outlet')
      .where('sale.id = :id', { id });

    // Apply access control
    if (user.role === UserRole.EMPLOYEE) {
      queryBuilder.andWhere('sale.outletId = :userOutletId', {
        userOutletId: user.outletId,
      });
    }

    const sale = await queryBuilder.getOne();

    if (!sale) {
      throw new NotFoundException('Sale not found');
    }

    return sale;
  }

  async getDailySalesReport(date: string, outletId?: string) {
    const startDate = new Date(date);
    startDate.setHours(0, 0, 0, 0);
    
    const endDate = new Date(date);
    endDate.setHours(23, 59, 59, 999);

    const queryBuilder = this.saleRepository
      .createQueryBuilder('sale')
      .select([
        'COUNT(sale.id) as totalSales',
        'SUM(sale.grandTotal) as totalRevenue',
        'AVG(sale.grandTotal) as averageSale',
        'SUM(sale.tax) as totalTax',
        'SUM(sale.discount) as totalDiscount',
      ])
      .where('sale.saleDate BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      });

    if (outletId) {
      queryBuilder.andWhere('sale.outletId = :outletId', { outletId });
    }

    const result = await queryBuilder.getRawOne();

    return {
      date,
      outletId,
      ...result,
    };
  }

  async getSalesSummary(startDate: string, endDate: string, outletId?: string) {
    const queryBuilder = this.saleRepository
      .createQueryBuilder('sale')
      .select([
        'DATE(sale.saleDate) as date',
        'COUNT(sale.id) as totalSales',
        'SUM(sale.grandTotal) as totalRevenue',
      ])
      .where('sale.saleDate BETWEEN :startDate AND :endDate', {
        startDate: new Date(startDate),
        endDate: new Date(endDate),
      })
      .groupBy('DATE(sale.saleDate)')
      .orderBy('date', 'ASC');

    if (outletId) {
      queryBuilder.andWhere('sale.outletId = :outletId', { outletId });
    }

    return queryBuilder.getRawMany();
  }

  private async validateSaleItems(items: any[]) {
    for (const item of items) {
      const product = await this.productRepository.findOne({
        where: { id: item.productId, isActive: true },
      });

      if (!product) {
        throw new BadRequestException(`Product ${item.productId} not found`);
      }

      if (product.stockQuantity < item.quantity) {
        throw new BadRequestException(
          `Insufficient stock for product ${product.name}`,
        );
      }

      // Validate price and total
      const expectedTotal = item.price * item.quantity;
      if (Math.abs(expectedTotal - item.total) > 0.01) {
        throw new BadRequestException(
          `Invalid total for item ${product.name}`,
        );
      }
    }
  }
}
