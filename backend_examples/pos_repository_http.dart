// New HTTP-based POS Repository to replace Firebase Firestore
import 'package:dio/dio.dart';
import 'package:dartz/dartz.dart';
import 'package:hive/hive.dart';
import 'package:pos_app/core/constants/api_constants.dart';
import 'package:pos_app/core/constants/app_constants.dart';
import 'package:pos_app/core/errors/failures.dart';
import 'package:pos_app/core/utils/logger.dart';
import 'package:pos_app/core/utils/network_info.dart';
import 'package:pos_app/models/sale.dart';

abstract class POSRepository {
  Future<Either<Failure, Sale>> createSale(Sale sale);
  Future<Either<Failure, List<Sale>>> getSales({
    String? outletId,
    DateTime? startDate,
    DateTime? endDate,
    int page = 1,
    int limit = 20,
  });
  Future<Either<Failure, void>> syncOfflineSales();
}

class POSRepositoryHttpImpl implements POSRepository {
  final Dio _dio;
  final NetworkInfo _networkInfo;
  final Box _saleBox;

  POSRepositoryHttpImpl({
    required Dio dio,
    required NetworkInfo networkInfo,
    required Box saleBox,
  })  : _dio = dio,
        _networkInfo = networkInfo,
        _saleBox = saleBox;

  @override
  Future<Either<Failure, Sale>> createSale(Sale sale) async {
    try {
      final isConnected = await _networkInfo.isConnected;

      if (!isConnected) {
        // Save offline with temporary ID
        final offlineSale = sale.copyWith(
          id: 'offline_${DateTime.now().millisecondsSinceEpoch}',
          isSynced: false,
        );
        
        await _saleBox.add({
          ...offlineSale.toMap(),
          '_offline_key': _saleBox.length,
        });
        
        AppLogger.info('Sale saved offline: ${offlineSale.id}');
        return Right(offlineSale);
      }

      // Save online
      final response = await _dio.post(
        ApiConstants.salesEndpoint,
        data: sale.toMap(),
      );

      if (response.statusCode == 201) {
        final newSale = Sale.fromMap(response.data);
        AppLogger.info('Sale created online: ${newSale.id}');
        return Right(newSale);
      } else {
        return Left(ServerFailure('Failed to create sale: ${response.statusMessage}'));
      }
    } on DioException catch (e) {
      AppLogger.error('Create sale HTTP error', e);
      
      if (e.type == DioExceptionType.connectionTimeout ||
          e.type == DioExceptionType.receiveTimeout ||
          e.type == DioExceptionType.connectionError) {
        // Network error, save offline
        final offlineSale = sale.copyWith(
          id: 'offline_${DateTime.now().millisecondsSinceEpoch}',
          isSynced: false,
        );
        
        await _saleBox.add({
          ...offlineSale.toMap(),
          '_offline_key': _saleBox.length,
        });
        
        return Right(offlineSale);
      }
      
      return Left(ServerFailure('Network error: ${e.message}'));
    } catch (e) {
      AppLogger.error('Create sale error', e);
      return Left(UnknownFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Sale>>> getSales({
    String? outletId,
    DateTime? startDate,
    DateTime? endDate,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final isConnected = await _networkInfo.isConnected;
      if (!isConnected) {
        return const Left(NetworkFailure(AppConstants.networkErrorMessage));
      }

      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };

      if (outletId != null) queryParams['outlet_id'] = outletId;
      if (startDate != null) {
        queryParams['start_date'] = startDate.toIso8601String();
      }
      if (endDate != null) {
        queryParams['end_date'] = endDate.toIso8601String();
      }

      final response = await _dio.get(
        ApiConstants.salesEndpoint,
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        final data = response.data;
        final salesList = (data['data'] as List)
            .map((saleData) => Sale.fromMap(saleData))
            .toList();

        return Right(salesList);
      } else {
        return Left(ServerFailure('Failed to fetch sales: ${response.statusMessage}'));
      }
    } on DioException catch (e) {
      AppLogger.error('Get sales HTTP error', e);
      return Left(ServerFailure('Network error: ${e.message}'));
    } catch (e) {
      AppLogger.error('Get sales error', e);
      return Left(UnknownFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> syncOfflineSales() async {
    try {
      final isConnected = await _networkInfo.isConnected;
      if (!isConnected) {
        return const Left(NetworkFailure(AppConstants.networkErrorMessage));
      }

      final offlineData = _saleBox.values.toList();
      final offlineSales = offlineData
          .map((data) {
            final saleMap = Map<String, dynamic>.from(data);
            return {
              'sale': Sale.fromMap(saleMap),
              'key': saleMap['_offline_key'],
            };
          })
          .where((item) => !item['sale'].isSynced)
          .toList();

      if (offlineSales.isEmpty) {
        AppLogger.info('No offline sales to sync');
        return const Right(null);
      }

      AppLogger.info('Syncing ${offlineSales.length} offline sales...');

      // Batch sync for better performance
      final batchData = offlineSales.map((item) => item['sale'].toMap()).toList();
      
      final response = await _dio.post(
        '${ApiConstants.salesEndpoint}/batch',
        data: {'sales': batchData},
      );

      if (response.statusCode == 201) {
        final syncedSales = (response.data['data'] as List)
            .map((saleData) => Sale.fromMap(saleData))
            .toList();

        // Remove synced sales from offline storage
        for (int i = 0; i < offlineSales.length; i++) {
          final offlineKey = offlineSales[i]['key'];
          await _saleBox.delete(offlineKey);
        }

        AppLogger.info('Successfully synced ${syncedSales.length} sales');
        return const Right(null);
      } else {
        return Left(ServerFailure('Batch sync failed: ${response.statusMessage}'));
      }
    } on DioException catch (e) {
      AppLogger.error('Sync offline sales HTTP error', e);
      return Left(ServerFailure('Sync failed: ${e.message}'));
    } catch (e) {
      AppLogger.error('Sync offline sales error', e);
      return Left(UnknownFailure(e.toString()));
    }
  }

  // Helper method to get offline sales count
  Future<int> getOfflineSalesCount() async {
    try {
      final offlineData = _saleBox.values.toList();
      final offlineSales = offlineData
          .map((data) => Sale.fromMap(Map<String, dynamic>.from(data)))
          .where((sale) => !sale.isSynced)
          .length;
      
      return offlineSales;
    } catch (e) {
      AppLogger.error('Get offline sales count error', e);
      return 0;
    }
  }

  // Helper method to clear all offline data (for testing)
  Future<void> clearOfflineData() async {
    try {
      await _saleBox.clear();
      AppLogger.info('Offline sales data cleared');
    } catch (e) {
      AppLogger.error('Clear offline data error', e);
    }
  }
}

// Enhanced API Constants with more endpoints
extension ApiConstantsExtended on ApiConstants {
  static const String batchSalesEndpoint = '$salesEndpoint/batch';
  static const String salesReportsEndpoint = '$salesEndpoint/reports';
  static const String salesStatsEndpoint = '$salesEndpoint/stats';
}

// HTTP Client Configuration
class DioConfig {
  static Dio createDio() {
    final dio = Dio(BaseOptions(
      baseUrl: ApiConstants.baseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      sendTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));

    // Add logging interceptor for development
    dio.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: true,
      logPrint: (object) => AppLogger.debug(object.toString()),
    ));

    return dio;
  }
}
