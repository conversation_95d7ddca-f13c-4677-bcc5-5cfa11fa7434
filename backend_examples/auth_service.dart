// New HTTP-based Auth Service to replace Firebase Auth
import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:jwt_decoder/jwt_decoder.dart';
import 'package:pos_app/core/constants/api_constants.dart';
import 'package:pos_app/core/errors/failures.dart';
import 'package:pos_app/models/user.dart';
import 'package:dartz/dartz.dart';

abstract class AuthService {
  Future<Either<Failure, User>> signIn(String email, String password);
  Future<Either<Failure, void>> signOut();
  Future<Either<Failure, bool>> isSignedIn();
  Future<Either<Failure, User>> getCurrentUser();
  Future<Either<Failure, String>> refreshToken();
}

class AuthServiceImpl implements AuthService {
  final Dio _dio;
  final SharedPreferences _prefs;
  
  static const String _accessTokenKey = 'access_token';
  static const String _refreshTokenKey = 'refresh_token';
  static const String _userDataKey = 'user_data';

  AuthServiceImpl({
    required Dio dio,
    required SharedPreferences prefs,
  }) : _dio = dio, _prefs = prefs {
    // Setup interceptor for automatic token refresh
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          final token = await _getAccessToken();
          if (token != null) {
            options.headers['Authorization'] = 'Bearer $token';
          }
          handler.next(options);
        },
        onError: (error, handler) async {
          if (error.response?.statusCode == 401) {
            // Token expired, try to refresh
            final refreshResult = await refreshToken();
            if (refreshResult.isRight()) {
              // Retry the original request
              final token = await _getAccessToken();
              error.requestOptions.headers['Authorization'] = 'Bearer $token';
              final response = await _dio.fetch(error.requestOptions);
              handler.resolve(response);
              return;
            }
          }
          handler.next(error);
        },
      ),
    );
  }

  @override
  Future<Either<Failure, User>> signIn(String email, String password) async {
    try {
      final response = await _dio.post(
        ApiConstants.loginEndpoint,
        data: {
          'email': email,
          'password': password,
        },
      );

      if (response.statusCode == 200) {
        final data = response.data;
        
        // Store tokens
        await _prefs.setString(_accessTokenKey, data['access_token']);
        await _prefs.setString(_refreshTokenKey, data['refresh_token']);
        
        // Store user data
        final user = User.fromMap(data['user']);
        await _prefs.setString(_userDataKey, jsonEncode(user.toMap()));
        
        return Right(user);
      } else {
        return Left(AuthFailure('Login failed: ${response.statusMessage}'));
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        return const Left(AuthFailure('Invalid email or password'));
      } else if (e.response?.statusCode == 429) {
        return const Left(AuthFailure('Too many login attempts. Try again later'));
      } else {
        return Left(AuthFailure('Network error: ${e.message}'));
      }
    } catch (e) {
      return Left(UnknownFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> signOut() async {
    try {
      final refreshToken = _prefs.getString(_refreshTokenKey);
      
      if (refreshToken != null) {
        // Call logout endpoint to invalidate tokens
        await _dio.post(
          ApiConstants.logoutEndpoint,
          data: {'refresh_token': refreshToken},
        );
      }
      
      // Clear local storage
      await _prefs.remove(_accessTokenKey);
      await _prefs.remove(_refreshTokenKey);
      await _prefs.remove(_userDataKey);
      
      return const Right(null);
    } catch (e) {
      // Even if logout fails, clear local storage
      await _prefs.remove(_accessTokenKey);
      await _prefs.remove(_refreshTokenKey);
      await _prefs.remove(_userDataKey);
      
      return const Right(null);
    }
  }

  @override
  Future<Either<Failure, bool>> isSignedIn() async {
    try {
      final token = _prefs.getString(_accessTokenKey);
      
      if (token == null) return const Right(false);
      
      // Check if token is expired
      if (JwtDecoder.isExpired(token)) {
        // Try to refresh token
        final refreshResult = await refreshToken();
        return Right(refreshResult.isRight());
      }
      
      return const Right(true);
    } catch (e) {
      return const Right(false);
    }
  }

  @override
  Future<Either<Failure, User>> getCurrentUser() async {
    try {
      final userDataString = _prefs.getString(_userDataKey);
      
      if (userDataString == null) {
        return const Left(AuthFailure('User not authenticated'));
      }
      
      final userData = jsonDecode(userDataString);
      final user = User.fromMap(userData);
      
      return Right(user);
    } catch (e) {
      return Left(UnknownFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, String>> refreshToken() async {
    try {
      final refreshToken = _prefs.getString(_refreshTokenKey);
      
      if (refreshToken == null) {
        return const Left(AuthFailure('No refresh token available'));
      }
      
      final response = await _dio.post(
        ApiConstants.refreshTokenEndpoint,
        data: {'refresh_token': refreshToken},
      );
      
      if (response.statusCode == 200) {
        final data = response.data;
        final newAccessToken = data['access_token'];
        
        await _prefs.setString(_accessTokenKey, newAccessToken);
        
        // Update refresh token if provided
        if (data['refresh_token'] != null) {
          await _prefs.setString(_refreshTokenKey, data['refresh_token']);
        }
        
        return Right(newAccessToken);
      } else {
        return const Left(AuthFailure('Token refresh failed'));
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        // Refresh token is invalid, user needs to login again
        await signOut();
        return const Left(AuthFailure('Session expired. Please login again'));
      }
      return Left(AuthFailure('Network error: ${e.message}'));
    } catch (e) {
      return Left(UnknownFailure(e.toString()));
    }
  }

  Future<String?> _getAccessToken() async {
    return _prefs.getString(_accessTokenKey);
  }
}

// API Constants
class ApiConstants {
  static const String baseUrl = 'https://your-api.com/api/v1';
  static const String loginEndpoint = '$baseUrl/auth/login';
  static const String logoutEndpoint = '$baseUrl/auth/logout';
  static const String refreshTokenEndpoint = '$baseUrl/auth/refresh';
  
  // Other endpoints
  static const String usersEndpoint = '$baseUrl/users';
  static const String outletsEndpoint = '$baseUrl/outlets';
  static const String productsEndpoint = '$baseUrl/products';
  static const String salesEndpoint = '$baseUrl/sales';
  static const String attendanceEndpoint = '$baseUrl/attendance';
}
